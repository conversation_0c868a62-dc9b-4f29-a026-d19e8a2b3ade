from django import forms
from django.core.exceptions import ValidationError
from datetime import date, timedelta
from .models import (
    Employee, Department, Designation, LeaveType, 
    HolidayCalendar, LeaveBalance, LeaveRequest, Attendance
)

class EmployeeForm(forms.ModelForm):
    """Form for creating/editing employee profiles"""
    class Meta:
        model = Employee
        fields = ['employee_code', 'department', 'designation', 'date_of_joining', 'status', 'manager']
        widgets = {
            'employee_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., EMP001'}),
            'department': forms.Select(attrs={'class': 'form-control'}),
            'designation': forms.Select(attrs={'class': 'form-control'}),
            'date_of_joining': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'manager': forms.Select(attrs={'class': 'form-control'}),
        }

class DepartmentForm(forms.ModelForm):
    """Form for creating/editing departments"""
    class Meta:
        model = Department
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Department Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Department Description'}),
        }

class DesignationForm(forms.ModelForm):
    """Form for creating/editing designations"""
    class Meta:
        model = Designation
        fields = ['title', 'description']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Designation Title'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Designation Description'}),
        }

class LeaveTypeForm(forms.ModelForm):
    """Form for creating/editing leave types"""
    class Meta:
        model = LeaveType
        fields = ['name', 'description', 'max_days_per_year', 'is_carry_forward', 'max_carry_forward_days', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Leave Type Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'max_days_per_year': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'max_carry_forward_days': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'is_carry_forward': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class HolidayForm(forms.ModelForm):
    """Form for creating/editing holidays"""
    class Meta:
        model = HolidayCalendar
        fields = ['date', 'name', 'description', 'is_optional']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Holiday Name'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_optional': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class LeaveRequestForm(forms.ModelForm):
    """Form for creating leave requests"""
    class Meta:
        model = LeaveRequest
        fields = ['leave_type', 'start_date', 'end_date', 'reason', 'is_half_day', 'half_day_period', 'emergency_contact']
        widgets = {
            'leave_type': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Reason for leave...'}),
            'is_half_day': forms.CheckboxInput(attrs={'class': 'form-check-input', 'id': 'halfDayCheck'}),
            'half_day_period': forms.Select(attrs={'class': 'form-control', 'id': 'halfDayPeriod'}),
            'emergency_contact': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Emergency contact number'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.employee = kwargs.pop('employee', None)
        super().__init__(*args, **kwargs)
        
        # Only show active leave types
        self.fields['leave_type'].queryset = LeaveType.objects.filter(is_active=True)
        
        # Set minimum date to today
        self.fields['start_date'].widget.attrs['min'] = date.today().isoformat()
        self.fields['end_date'].widget.attrs['min'] = date.today().isoformat()
    
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        leave_type = cleaned_data.get('leave_type')
        is_half_day = cleaned_data.get('is_half_day')
        
        # Validate dates
        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError("Start date cannot be after end date.")
            
            if start_date < date.today():
                raise ValidationError("Cannot apply for leave in the past.")
        
        # Validate half day
        if is_half_day and start_date != end_date:
            raise ValidationError("Half day leave can only be for a single day.")
        
        # Check leave balance
        if self.employee and leave_type and start_date and end_date:
            current_year = start_date.year
            try:
                leave_balance = LeaveBalance.objects.get(
                    employee=self.employee,
                    leave_type=leave_type,
                    year=current_year
                )
                
                # Calculate requested days
                if is_half_day:
                    requested_days = 0.5
                else:
                    # Simple calculation - can be enhanced to exclude weekends/holidays
                    requested_days = (end_date - start_date).days + 1
                
                if requested_days > leave_balance.remaining_days:
                    raise ValidationError(
                        f"Insufficient leave balance. Available: {leave_balance.remaining_days} days, "
                        f"Requested: {requested_days} days."
                    )
            except LeaveBalance.DoesNotExist:
                raise ValidationError(f"No leave balance found for {leave_type.name} in {current_year}.")
        
        return cleaned_data

class AttendanceForm(forms.ModelForm):
    """Form for marking attendance"""
    class Meta:
        model = Attendance
        fields = ['date', 'check_in', 'check_out', 'status', 'break_time', 'remarks', 'location']
        widgets = {
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'check_in': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'check_out': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'break_time': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.25', 'min': '0', 'placeholder': 'Hours'}),
            'remarks': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Work location'}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        check_in = cleaned_data.get('check_in')
        check_out = cleaned_data.get('check_out')
        status = cleaned_data.get('status')
        
        # Validate check-in and check-out times
        if status == 'Present' and not check_in:
            raise ValidationError("Check-in time is required for Present status.")
        
        if check_in and check_out and check_out <= check_in:
            raise ValidationError("Check-out time must be after check-in time.")
        
        return cleaned_data

class LeaveApprovalForm(forms.ModelForm):
    """Form for approving/rejecting leave requests"""
    class Meta:
        model = LeaveRequest
        fields = ['status', 'rejection_reason']
        widgets = {
            'status': forms.Select(attrs={'class': 'form-control'}),
            'rejection_reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Reason for rejection (if applicable)'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only allow Approved/Rejected status
        self.fields['status'].choices = [
            ('Approved', 'Approved'),
            ('Rejected', 'Rejected'),
        ]
    
    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        rejection_reason = cleaned_data.get('rejection_reason')
        
        if status == 'Rejected' and not rejection_reason:
            raise ValidationError("Rejection reason is required when rejecting a leave request.")
        
        return cleaned_data
