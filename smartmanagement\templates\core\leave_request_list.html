{% extends 'base.html' %}

{% block title %}Leave Requests - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending { background: #fef3c7; color: #92400e; }
    .status-approved { background: #d1fae5; color: #065f46; }
    .status-rejected { background: #fee2e2; color: #991b1b; }
    .status-cancelled { background: #f3f4f6; color: #374151; }
    
    .leave-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
    }
    
    .leave-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-list me-3"></i>
                            {% if is_admin %}All Leave Requests{% else %}My Leave Requests{% endif %}
                        </h1>
                        <p class="lead mb-0">Manage and track leave requests</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_request_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Apply for Leave
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body py-3">
                    <form method="get" class="d-flex align-items-end gap-3 flex-wrap">
                        <div class="flex-fill" style="min-width: 180px;">
                            <label class="form-label mb-1 small">Status</label>
                            <select name="status" class="form-control form-control-sm">
                                <option value="">All Status</option>
                                <option value="Pending">Pending</option>
                                <option value="Approved">Approved</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="flex-fill" style="min-width: 150px;">
                            <label class="form-label mb-1 small">From Date</label>
                            <input type="date" name="from_date" class="form-control form-control-sm">
                        </div>
                        <div class="flex-fill" style="min-width: 150px;">
                            <label class="form-label mb-1 small">To Date</label>
                            <input type="date" name="to_date" class="form-control form-control-sm">
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>FILTER
                            </button>
                            <a href="{% url 'core:leave_request_list' %}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-refresh me-1"></i>RESET
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Leave Requests -->
    <div class="row">
        <div class="col-lg-12">
            {% for leave_request in page_obj %}
            <div class="leave-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <h5 class="mb-0 me-3">{{ leave_request.leave_type.name }}</h5>
                            <span class="status-badge status-{{ leave_request.status|lower }}">
                                {{ leave_request.status }}
                            </span>
                        </div>
                        
                        {% if is_admin %}
                        <p class="mb-1"><strong>Employee:</strong> {{ leave_request.employee.full_name }} ({{ leave_request.employee.employee_code }})</p>
                        {% endif %}
                        
                        <p class="mb-1">
                            <strong>Duration:</strong> 
                            {{ leave_request.start_date|date:"M d, Y" }} to {{ leave_request.end_date|date:"M d, Y" }}
                            ({{ leave_request.duration_text }})
                        </p>
                        
                        <p class="mb-1"><strong>Applied on:</strong> {{ leave_request.applied_on|date:"M d, Y" }}</p>
                        
                        {% if leave_request.approved_by %}
                        <p class="mb-1">
                            <strong>{% if leave_request.status == 'Approved' %}Approved{% else %}Processed{% endif %} by:</strong> 
                            {{ leave_request.approved_by.full_name }}
                            {% if leave_request.approved_on %}on {{ leave_request.approved_on|date:"M d, Y" }}{% endif %}
                        </p>
                        {% endif %}
                        
                        <p class="mb-0"><strong>Reason:</strong> {{ leave_request.reason|truncatechars:100 }}</p>
                        
                        {% if leave_request.rejection_reason %}
                        <p class="mb-0 text-danger"><strong>Rejection Reason:</strong> {{ leave_request.rejection_reason }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-4 text-end">
                        <div class="btn-group-vertical" role="group">
                            <a href="{% url 'core:leave_request_detail' leave_request.pk %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            
                            {% if leave_request.status == 'Pending' %}
                                {% if is_admin %}
                                <a href="{% url 'core:leave_request_approve' leave_request.pk %}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-check me-1"></i>Approve/Reject
                                </a>
                                {% else %}
                                <a href="{% url 'core:leave_request_edit' leave_request.pk %}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                                <a href="{% url 'core:leave_request_cancel' leave_request.pk %}" class="btn btn-outline-danger btn-sm" 
                                   onclick="return confirm('Are you sure you want to cancel this leave request?')">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </a>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Leave Requests Found</h4>
                <p class="text-muted">{% if not is_admin %}You haven't applied for any leaves yet.{% else %}No leave requests in the system.{% endif %}</p>
                <a href="{% url 'core:leave_request_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Apply for Leave
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="row mt-4">
        <div class="col-lg-12">
            <nav aria-label="Leave requests pagination">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">&laquo; First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
