from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Department, Designation, Employee, LeaveType,
    HolidayCalendar, LeaveBalance, LeaveRequest, Attendance
)

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    search_fields = ['name']
    list_filter = ['created_at']

@admin.register(Designation)
class DesignationAdmin(admin.ModelAdmin):
    list_display = ['title', 'description', 'created_at']
    search_fields = ['title']
    list_filter = ['created_at']

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_code', 'full_name', 'department', 'designation', 'status', 'date_of_joining']
    list_filter = ['status', 'department', 'designation', 'date_of_joining']
    search_fields = ['employee_code', 'user__first_name', 'user__last_name', 'user__email']
    raw_id_fields = ['user', 'manager']

    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = 'Full Name'

@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'max_days_per_year', 'is_carry_forward', 'is_active']
    list_filter = ['is_active', 'is_carry_forward']
    search_fields = ['name']

@admin.register(HolidayCalendar)
class HolidayCalendarAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'is_optional']
    list_filter = ['is_optional', 'date']
    search_fields = ['name']
    date_hierarchy = 'date'

@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'year', 'total_allocated', 'used_days', 'remaining_days']
    list_filter = ['year', 'leave_type']
    search_fields = ['employee__employee_code', 'employee__user__first_name', 'employee__user__last_name']

    def remaining_days(self, obj):
        return obj.remaining_days
    remaining_days.short_description = 'Remaining Days'

@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'start_date', 'end_date', 'total_days', 'status', 'applied_on']
    list_filter = ['status', 'leave_type', 'applied_on', 'start_date']
    search_fields = ['employee__employee_code', 'employee__user__first_name', 'employee__user__last_name']
    readonly_fields = ['total_days', 'applied_on']
    raw_id_fields = ['employee', 'approved_by']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee__user', 'leave_type', 'approved_by__user')

@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'date', 'check_in', 'check_out', 'working_hours', 'status']
    list_filter = ['status', 'date']
    search_fields = ['employee__employee_code', 'employee__user__first_name', 'employee__user__last_name']
    date_hierarchy = 'date'
    readonly_fields = ['working_hours', 'overtime_hours']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('employee__user')
