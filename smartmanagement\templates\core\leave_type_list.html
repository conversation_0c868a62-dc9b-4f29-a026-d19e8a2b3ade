{% extends 'base.html' %}

{% block title %}Leave Types Management - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2"><i class="fas fa-list-alt me-3"></i>Leave Types Management</h1>
                        <p class="lead mb-0">Configure and manage leave types</p>
                    </div>
                    <div>
                        <a href="{% url 'core:admin_dashboard' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <a href="{% url 'core:leave_type_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Add Leave Type
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_leave_types }}</h4>
                            <p class="mb-0">Total Leave Types</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ active_leave_types }}</h4>
                            <p class="mb-0">Active Leave Types</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ inactive_leave_types }}</h4>
                            <p class="mb-0">Inactive Leave Types</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="Search leave types by name or description...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Leave Types List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Leave Types</h5>
            <span class="badge bg-primary">{{ leave_types.paginator.count }} leave type{{ leave_types.paginator.count|pluralize }}</span>
        </div>
        <div class="card-body p-0">
            {% if leave_types %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Leave Type</th>
                                <th>Max Days/Year</th>
                                <th>Carry Forward</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave_type in leave_types %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-calendar-times text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ leave_type.name }}</h6>
                                            {% if leave_type.description %}
                                                <small class="text-muted">{{ leave_type.description|truncatewords:8 }}</small>
                                            {% else %}
                                                <small class="text-muted fst-italic">No description</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if leave_type.max_days_per_year > 0 %}
                                        <span class="badge bg-info">{{ leave_type.max_days_per_year }} days</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Unlimited</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave_type.is_carry_forward %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Yes
                                            {% if leave_type.max_carry_forward_days > 0 %}
                                                ({{ leave_type.max_carry_forward_days }} days)
                                            {% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>No
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave_type.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ leave_type.created_at|date:"M d, Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'core:leave_type_detail' leave_type.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'core:leave_type_edit' leave_type.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                data-bs-toggle="modal" data-bs-target="#deleteModal{{ leave_type.pk }}" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No leave types found</h5>
                    {% if search_query %}
                        <p class="text-muted">No leave types match your search criteria.</p>
                        <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Clear Search
                        </a>
                    {% else %}
                        <p class="text-muted">Start by creating your first leave type.</p>
                        <a href="{% url 'core:leave_type_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Leave Type
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
        
        <!-- Pagination -->
        {% if leave_types.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="Leave type pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if leave_types.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ leave_types.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ leave_types.number }} of {{ leave_types.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if leave_types.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ leave_types.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ leave_types.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modals -->
{% for leave_type in leave_types %}
<div class="modal fade" id="deleteModal{{ leave_type.pk }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Leave Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the leave type <strong>"{{ leave_type.name }}"</strong>?</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This action cannot be undone. Make sure this leave type is not being used in any leave requests or balances.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{% url 'core:leave_type_delete' leave_type.pk %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete Leave Type</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>
{% endblock %}
