{% extends 'base.html' %}

{% block title %}Attendance Records - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-present { background: #d1fae5; color: #065f46; }
    .status-absent { background: #fee2e2; color: #991b1b; }
    .status-leave { background: #fef3c7; color: #92400e; }
    .status-half-day { background: #e0e7ff; color: #3730a3; }
    .status-wfh { background: #f3e8ff; color: #6b21a8; }
    .status-holiday { background: #f0f9ff; color: #0c4a6e; }
    
    .attendance-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
    }
    
    .attendance-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.12);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .calendar-view {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .calendar-day {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2px;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .day-present { background: #d1fae5; color: #065f46; }
    .day-absent { background: #fee2e2; color: #991b1b; }
    .day-leave { background: #fef3c7; color: #92400e; }
    .day-holiday { background: #f0f9ff; color: #0c4a6e; }
    .day-future { background: #f3f4f6; color: #6b7280; }

    /* Today's Status Card Styles */
    .today-status-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        margin-bottom: 2rem;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .status-item label {
        font-weight: 600;
        margin-bottom: 0;
    }

    .status-value {
        font-weight: 500;
        font-size: 1.1em;
    }

    .status-badge.present {
        background-color: #28a745;
        color: white;
    }

    .status-badge.absent {
        background-color: #dc3545;
        color: white;
    }

    .status-badge.not-marked {
        background-color: #6c757d;
        color: white;
    }

    .quick-actions-inline button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-clock me-3"></i>
                            Attendance Records
                        </h1>
                        <p class="lead mb-0">Track and manage attendance records</p>
                    </div>
                    <div>
                        <a href="{% url 'core:dashboard' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <a href="{% url 'core:mark_attendance' %}" class="btn btn-light me-2">
                            <i class="fas fa-plus me-2"></i>Mark Attendance
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success" id="quickCheckInBtn" onclick="performQuickCheckIn()">
                                <i class="fas fa-sign-in-alt me-2"></i>Quick Check-in
                            </button>
                            <button type="button" class="btn btn-warning" id="quickCheckOutBtn" onclick="performQuickCheckOut()">
                                <i class="fas fa-sign-out-alt me-2"></i>Quick Check-out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Today's Status Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="today-status-card" id="todayStatusCard">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-3"><i class="fas fa-calendar-day me-2"></i>Today's Attendance Status</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="status-item">
                                    <label>Check-in:</label>
                                    <span id="todayCheckIn" class="status-value">Loading...</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <label>Check-out:</label>
                                    <span id="todayCheckOut" class="status-value">Loading...</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <label>Status:</label>
                                    <span id="todayStatus" class="status-badge">Loading...</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="status-item">
                                    <label>Working Hours:</label>
                                    <span id="todayWorkingHours" class="status-value">0.0 hrs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="quick-actions-inline">
                            <button type="button" class="btn btn-success btn-sm me-2" id="listCheckInBtn" onclick="performQuickCheckIn()">
                                <i class="fas fa-sign-in-alt me-1"></i>Check In
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="listCheckOutBtn" onclick="performQuickCheckOut()">
                                <i class="fas fa-sign-out-alt me-1"></i>Check Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ present_days }}</div>
                <div>Present Days</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ absent_days }}</div>
                <div>Absent Days</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ leave_days }}</div>
                <div>Leave Days</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ total_hours|floatformat:1 }}</div>
                <div>Total Hours</div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Month</label>
                            <select name="month" class="form-control">
                                <option value="">Current Month</option>
                                <option value="1">January</option>
                                <option value="2">February</option>
                                <option value="3">March</option>
                                <option value="4">April</option>
                                <option value="5">May</option>
                                <option value="6">June</option>
                                <option value="7">July</option>
                                <option value="8">August</option>
                                <option value="9">September</option>
                                <option value="10">October</option>
                                <option value="11">November</option>
                                <option value="12">December</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Year</label>
                            <select name="year" class="form-control">
                                <option value="2025">2025</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="Present">Present</option>
                                <option value="Absent">Absent</option>
                                <option value="Leave">Leave</option>
                                <option value="Half-Day">Half-Day</option>
                                <option value="WFH">Work From Home</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                                <a href="{% url 'core:attendance_list' %}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-refresh me-2"></i>Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Attendance Records -->
    <div class="row">
        <div class="col-lg-8">
            <h4 class="mb-3"><i class="fas fa-list me-2"></i>Recent Attendance</h4>
            
            <!-- Sample attendance records -->
            <div class="attendance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-3">Today - August 07, 2025</h6>
                            <span class="status-badge status-present">Present</span>
                        </div>
                        <p class="mb-1"><strong>Check-in:</strong> 09:15 AM</p>
                        <p class="mb-1"><strong>Check-out:</strong> 06:30 PM</p>
                        <p class="mb-1"><strong>Working Hours:</strong> 8.25 hours</p>
                        <p class="mb-0"><strong>Location:</strong> Office</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="attendance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-3">August 06, 2025</h6>
                            <span class="status-badge status-wfh">Work From Home</span>
                        </div>
                        <p class="mb-1"><strong>Check-in:</strong> 09:00 AM</p>
                        <p class="mb-1"><strong>Check-out:</strong> 06:00 PM</p>
                        <p class="mb-1"><strong>Working Hours:</strong> 8.0 hours</p>
                        <p class="mb-0"><strong>Location:</strong> Home</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="attendance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-3">August 05, 2025</h6>
                            <span class="status-badge status-leave">Leave</span>
                        </div>
                        <p class="mb-1"><strong>Leave Type:</strong> Casual Leave</p>
                        <p class="mb-0"><strong>Reason:</strong> Personal work</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="text-muted">No action required</span>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <p class="text-muted">No more attendance records found.</p>
                <a href="{% url 'core:mark_attendance' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Mark Today's Attendance
                </a>
            </div>
        </div>
        
        <!-- Calendar View -->
        <div class="col-lg-4">
            <div class="calendar-view">
                <h5 class="mb-3"><i class="fas fa-calendar me-2"></i>August 2025</h5>
                
                <!-- Calendar Header -->
                <div class="row text-center mb-2">
                    <div class="col"><small class="text-muted">Sun</small></div>
                    <div class="col"><small class="text-muted">Mon</small></div>
                    <div class="col"><small class="text-muted">Tue</small></div>
                    <div class="col"><small class="text-muted">Wed</small></div>
                    <div class="col"><small class="text-muted">Thu</small></div>
                    <div class="col"><small class="text-muted">Fri</small></div>
                    <div class="col"><small class="text-muted">Sat</small></div>
                </div>
                
                <!-- Calendar Days -->
                <div class="row text-center mb-1">
                    <div class="col"><div class="calendar-day day-future"></div></div>
                    <div class="col"><div class="calendar-day day-future"></div></div>
                    <div class="col"><div class="calendar-day day-future"></div></div>
                    <div class="col"><div class="calendar-day day-future"></div></div>
                    <div class="col"><div class="calendar-day day-present">1</div></div>
                    <div class="col"><div class="calendar-day day-present">2</div></div>
                    <div class="col"><div class="calendar-day day-holiday">3</div></div>
                </div>
                
                <div class="row text-center mb-1">
                    <div class="col"><div class="calendar-day day-holiday">4</div></div>
                    <div class="col"><div class="calendar-day day-leave">5</div></div>
                    <div class="col"><div class="calendar-day day-present">6</div></div>
                    <div class="col"><div class="calendar-day day-present">7</div></div>
                    <div class="col"><div class="calendar-day day-future">8</div></div>
                    <div class="col"><div class="calendar-day day-future">9</div></div>
                    <div class="col"><div class="calendar-day day-future">10</div></div>
                </div>
                
                <!-- Legend -->
                <div class="mt-3">
                    <h6>Legend:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="status-badge status-present">Present</span>
                        <span class="status-badge status-absent">Absent</span>
                        <span class="status-badge status-leave">Leave</span>
                        <span class="status-badge status-holiday">Holiday</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}



{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load today's attendance status
    loadTodayAttendanceStatus();

    // Refresh status every 30 seconds
    setInterval(loadTodayAttendanceStatus, 30000);
});

// Load today's attendance status
function loadTodayAttendanceStatus() {
    fetch('{% url "core:get_attendance_status" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTodayStatusDisplay(data);
                updateListButtonStates(data);
            }
        })
        .catch(error => {
            console.error('Error loading attendance status:', error);
        });
}

// Update today's status display
function updateTodayStatusDisplay(data) {
    document.getElementById('todayCheckIn').textContent = data.check_in || 'Not marked';
    document.getElementById('todayCheckOut').textContent = data.check_out || 'Not marked';
    document.getElementById('todayWorkingHours').textContent = data.working_hours + ' hrs';

    const statusElement = document.getElementById('todayStatus');
    statusElement.textContent = data.status;
    statusElement.className = 'status-badge ' + (data.status === 'Present' ? 'present' :
                                                 data.status === 'Absent' ? 'absent' : 'not-marked');
}

// Update button states in list view
function updateListButtonStates(data) {
    const checkInBtn = document.getElementById('listCheckInBtn');
    const checkOutBtn = document.getElementById('listCheckOutBtn');

    if (data.check_in && !data.check_out) {
        // Checked in, not checked out
        checkInBtn.disabled = true;
        checkInBtn.classList.add('btn-secondary');
        checkInBtn.classList.remove('btn-success');
        checkOutBtn.disabled = false;
    } else if (data.check_in && data.check_out) {
        // Both checked in and out
        checkInBtn.disabled = true;
        checkOutBtn.disabled = true;
        checkInBtn.classList.add('btn-secondary');
        checkInBtn.classList.remove('btn-success');
        checkOutBtn.classList.add('btn-secondary');
        checkOutBtn.classList.remove('btn-warning');
    } else {
        // Not checked in
        checkInBtn.disabled = false;
        checkOutBtn.disabled = true;
        checkInBtn.classList.add('btn-success');
        checkInBtn.classList.remove('btn-secondary');
    }
}

// Quick check-in function for list view
window.performQuickCheckIn = function() {
    const checkInBtn = document.getElementById('listCheckInBtn');
    checkInBtn.disabled = true;
    checkInBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking in...';

    fetch('{% url "core:quick_check_in" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadTodayAttendanceStatus();
            showNotification('success', data.message);
        } else {
            checkInBtn.disabled = false;
            checkInBtn.innerHTML = '<i class="fas fa-sign-in-alt me-1"></i>Check In';
            showNotification('error', data.error);
        }
    })
    .catch(error => {
        checkInBtn.disabled = false;
        checkInBtn.innerHTML = '<i class="fas fa-sign-in-alt me-1"></i>Check In';
        showNotification('error', 'Network error occurred');
        console.error('Error:', error);
    });
};

// Quick check-out function for list view
window.performQuickCheckOut = function() {
    const checkOutBtn = document.getElementById('listCheckOutBtn');
    checkOutBtn.disabled = true;
    checkOutBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking out...';

    fetch('{% url "core:quick_check_out" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadTodayAttendanceStatus();
            showNotification('success', data.message + ' Working hours: ' + data.working_hours + ' hrs');
        } else {
            checkOutBtn.disabled = false;
            checkOutBtn.innerHTML = '<i class="fas fa-sign-out-alt me-1"></i>Check Out';
            showNotification('error', data.error);
        }
    })
    .catch(error => {
        checkOutBtn.disabled = false;
        checkOutBtn.innerHTML = '<i class="fas fa-sign-out-alt me-1"></i>Check Out';
        showNotification('error', 'Network error occurred');
        console.error('Error:', error);
    });
};

// Show notification
function showNotification(type, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
