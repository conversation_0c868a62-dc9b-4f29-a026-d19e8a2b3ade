{% extends 'base.html' %}

{% block title %}Designation Management - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2"><i class="fas fa-user-tie me-3"></i>Designation Management</h1>
                        <p class="lead mb-0">Manage job positions and roles</p>
                    </div>
                    <div>
                        <a href="{% url 'core:admin_dashboard' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <a href="{% url 'core:designation_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Add Designation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_designations }}</h4>
                            <p class="mb-0">Total Designations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ designations_with_employees }}</h4>
                            <p class="mb-0">Active Designations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ designations.paginator.count }}</h4>
                            <p class="mb-0">Search Results</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-search fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="Search designations by title or description...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <a href="{% url 'core:designation_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Designations List -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Designations</h5>
            <span class="badge bg-primary">{{ designations.paginator.count }} designation{{ designations.paginator.count|pluralize }}</span>
        </div>
        <div class="card-body p-0">
            {% if designations %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Designation Title</th>
                                <th>Description</th>
                                <th>Employees</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for designation in designations %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user-tie text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ designation.title }}</h6>
                                            <small class="text-muted">ID: {{ designation.id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if designation.description %}
                                        <span class="text-muted">{{ designation.description|truncatewords:10 }}</span>
                                    {% else %}
                                        <span class="text-muted fst-italic">No description</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ designation.employee_count }} employee{{ designation.employee_count|pluralize }}</span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ designation.created_at|date:"M d, Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'core:designation_detail' designation.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'core:designation_edit' designation.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                data-bs-toggle="modal" data-bs-target="#deleteModal{{ designation.pk }}" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No designations found</h5>
                    {% if search_query %}
                        <p class="text-muted">No designations match your search criteria.</p>
                        <a href="{% url 'core:designation_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Clear Search
                        </a>
                    {% else %}
                        <p class="text-muted">Start by creating your first designation.</p>
                        <a href="{% url 'core:designation_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Designation
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
        
        <!-- Pagination -->
        {% if designations.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="Designation pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if designations.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ designations.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ designations.number }} of {{ designations.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if designations.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ designations.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ designations.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modals -->
{% for designation in designations %}
<div class="modal fade" id="deleteModal{{ designation.pk }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Designation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the designation <strong>"{{ designation.title }}"</strong>?</p>
                {% if designation.employee_count > 0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This designation has {{ designation.employee_count }} employee{{ designation.employee_count|pluralize }}. 
                        Please reassign or deactivate them first.
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This action cannot be undone.
                    </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                {% if designation.employee_count == 0 %}
                    <form method="POST" action="{% url 'core:designation_delete' designation.pk %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Delete Designation</button>
                    </form>
                {% else %}
                    <button type="button" class="btn btn-danger" disabled>Cannot Delete</button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>
{% endblock %}
