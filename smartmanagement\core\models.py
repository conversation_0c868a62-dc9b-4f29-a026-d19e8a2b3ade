from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import date, timedelta

User = get_user_model()

class Department(models.Model):
    """Department model for organizing employees"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

class Designation(models.Model):
    """Designation/Position model for employees"""
    title = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['title']

    def __str__(self):
        return self.title

class Employee(models.Model):
    """Extended employee model linked to CustomUser"""
    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Inactive', 'Inactive'),
        ('Terminated', 'Terminated'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='employee_profile')
    employee_code = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    designation = models.ForeignKey(Designation, on_delete=models.SET_NULL, null=True, blank=True)
    date_of_joining = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='Active')
    manager = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='subordinates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['employee_code']

    def __str__(self):
        return f"{self.employee_code} - {self.user.get_full_name() or self.user.username}"

    @property
    def full_name(self):
        return self.user.get_full_name() or self.user.username

class LeaveType(models.Model):
    """Types of leaves available in the system"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    max_days_per_year = models.PositiveIntegerField(default=0, help_text="Maximum days allowed per year (0 for unlimited)")
    is_carry_forward = models.BooleanField(default=False, help_text="Can unused leaves be carried forward?")
    max_carry_forward_days = models.PositiveIntegerField(default=0, help_text="Maximum days that can be carried forward")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

class HolidayCalendar(models.Model):
    """Public holidays calendar"""
    date = models.DateField(unique=True)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    is_optional = models.BooleanField(default=False, help_text="Is this an optional holiday?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['date']

    def __str__(self):
        return f"{self.name} - {self.date}"

class LeaveBalance(models.Model):
    """Employee leave balance tracking"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_balances')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    year = models.PositiveIntegerField()
    total_allocated = models.PositiveIntegerField(default=0)
    used_days = models.DecimalField(max_digits=5, decimal_places=1, default=0)
    carry_forward = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['employee', 'leave_type', 'year']
        ordering = ['-year', 'leave_type__name']

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.year})"

    @property
    def available_days(self):
        return self.total_allocated + self.carry_forward - self.used_days

    @property
    def remaining_days(self):
        return max(0, self.available_days)

class LeaveRequest(models.Model):
    """Employee leave requests"""
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
        ('Cancelled', 'Cancelled'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_requests')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    total_days = models.DecimalField(max_digits=5, decimal_places=1, default=0)
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='Pending')
    applied_on = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves')
    approved_on = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    is_half_day = models.BooleanField(default=False)
    half_day_period = models.CharField(max_length=20, choices=[('Morning', 'Morning'), ('Afternoon', 'Afternoon')], blank=True, null=True)
    emergency_contact = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-applied_on']

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} to {self.end_date})"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError("Start date cannot be after end date")

    def save(self, *args, **kwargs):
        if not self.total_days:
            self.calculate_total_days()
        super().save(*args, **kwargs)

    def calculate_total_days(self):
        """Calculate total leave days excluding weekends and holidays"""
        if not self.start_date or not self.end_date:
            return 0

        if self.is_half_day:
            self.total_days = 0.5
            return

        total_days = 0
        current_date = self.start_date

        while current_date <= self.end_date:
            # Skip weekends (Saturday=5, Sunday=6)
            if current_date.weekday() < 5:  # Monday=0 to Friday=4
                # Check if it's not a holiday
                if not HolidayCalendar.objects.filter(date=current_date).exists():
                    total_days += 1
            current_date += timedelta(days=1)

        self.total_days = total_days

    @property
    def duration_text(self):
        if self.is_half_day:
            return f"Half Day ({self.half_day_period})"
        elif self.start_date == self.end_date:
            return "1 Day"
        else:
            return f"{self.total_days} Days"

class Attendance(models.Model):
    """Daily attendance tracking"""
    STATUS_CHOICES = [
        ('Present', 'Present'),
        ('Absent', 'Absent'),
        ('Leave', 'Leave'),
        ('Half-Day', 'Half-Day'),
        ('WFH', 'Work From Home'),
        ('Holiday', 'Holiday'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance_records')
    date = models.DateField()
    check_in = models.TimeField(null=True, blank=True)
    check_out = models.TimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='Absent')
    working_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0, help_text="Total working hours")
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    break_time = models.DecimalField(max_digits=4, decimal_places=2, default=0, help_text="Break time in hours")
    remarks = models.TextField(blank=True, null=True)
    location = models.CharField(max_length=100, blank=True, null=True, help_text="Work location")
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee__employee_code']

    def __str__(self):
        return f"{self.employee} - {self.date} ({self.status})"

    def calculate_working_hours(self):
        """Calculate working hours based on check-in and check-out times"""
        if self.check_in and self.check_out:
            from datetime import datetime, timedelta

            # Convert times to datetime objects for calculation
            check_in_dt = datetime.combine(self.date, self.check_in)
            check_out_dt = datetime.combine(self.date, self.check_out)

            # Handle overnight shifts
            if check_out_dt < check_in_dt:
                check_out_dt += timedelta(days=1)

            # Calculate total time
            total_time = check_out_dt - check_in_dt
            total_hours = total_time.total_seconds() / 3600

            # Subtract break time
            self.working_hours = max(0, total_hours - float(self.break_time))

            # Calculate overtime (assuming 8 hours is standard)
            standard_hours = 8
            if self.working_hours > standard_hours:
                self.overtime_hours = self.working_hours - standard_hours
            else:
                self.overtime_hours = 0

    def save(self, *args, **kwargs):
        if self.check_in and self.check_out:
            self.calculate_working_hours()
        super().save(*args, **kwargs)
