{% extends 'base.html' %}

{% block title %}{{ page_title }} - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-building me-3"></i>{{ page_title }}
                        </h1>
                        <p class="lead mb-0">{{ page_description }}</p>
                    </div>
                    <div>
                        <a href="{% url 'core:department_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Departments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Department Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {% csrf_token %}
                        
                        <!-- Department Name -->
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-building me-2"></i>Department Name *
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enter a unique name for the department</div>
                        </div>

                        <!-- Department Description -->
                        <div class="mb-4">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Provide a brief description of the department's purpose and responsibilities</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:department_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ form_action }} Department
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information Card -->
            {% if department %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Department Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Created:</strong> 
                                <span class="text-muted">{{ department.created_at|date:"M d, Y g:i A" }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Last Updated:</strong> 
                                <span class="text-muted">{{ department.updated_at|date:"M d, Y g:i A" }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'core:department_detail' department.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-2"></i>View Department Details
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    display: block !important;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation styling
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '' && this.hasAttribute('required')) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                this.classList.remove('is-invalid');
                if (this.value.trim() !== '') {
                    this.classList.add('is-valid');
                }
            }
        });
    });
    
    // Form submission validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        inputs.forEach(input => {
            if (input.hasAttribute('required') && input.value.trim() === '') {
                input.classList.add('is-invalid');
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            // Scroll to first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        }
    });
});
</script>
{% endblock %}
