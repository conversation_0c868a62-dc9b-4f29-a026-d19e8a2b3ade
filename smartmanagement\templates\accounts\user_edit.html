{% extends 'base.html' %}

{% block title %}Edit {{ user_detail.username }} - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-user-edit me-3"></i>
                            Edit User
                        </h1>
                        <p class="lead mb-0">Update {{ user_detail.first_name }} {{ user_detail.last_name }}'s information</p>
                    </div>
                    <div>
                        <a href="{% url 'user_detail' user_detail.id %}" class="btn btn-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                        <a href="{% url 'user_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-users me-2"></i>All Users
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Edit Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" id="editUserForm">
                            {% csrf_token %}
                            
                            <!-- Personal Information -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>First Name
                                    </label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.first_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>Last Name
                                    </label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.last_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.username.id_for_label }}" class="form-label">
                                        <i class="fas fa-at me-2"></i>Username
                                    </label>
                                    {{ form.username }}
                                    {% if form.username.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.username.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.role.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tag me-2"></i>Role
                                    </label>
                                    {{ form.role }}
                                    {% if form.role.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.role.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-2"></i>Phone Number
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.phone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Date of Birth
                                </label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.date_of_birth.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.address.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Account Status -->
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-cog me-2"></i>Account Status
                                    </h6>
                                    <div class="form-check">
                                        {{ form.is_active }}
                                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                            Account is active
                                        </label>
                                    </div>
                                    {% if form.is_active.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.is_active.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">Inactive users cannot log in to the system.</small>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="{% url 'user_detail' user_detail.id %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- User Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>User Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-primary">{{ user_detail.created_at|timesince }}</h4>
                                    <small class="text-muted">Member Since</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-success">{{ user_detail.last_login|timesince|default:"Never" }}</h4>
                                    <small class="text-muted">Last Login</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-warning">{{ user_detail.updated_at|timesince }}</h4>
                                    <small class="text-muted">Last Updated</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h4 class="{% if user_detail.is_active %}text-success{% else %}text-danger{% endif %}">
                                    {% if user_detail.is_active %}Active{% else %}Inactive{% endif %}
                                </h4>
                                <small class="text-muted">Status</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
        submitBtn.disabled = true;
        
        // Re-enable button after a delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Form validation
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('editUserForm');
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
        
        function validateField(field) {
            const value = field.value.trim();
            const fieldName = field.name;
            
            // Remove existing validation feedback
            const existingFeedback = field.parentNode.querySelector('.validation-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            
            field.classList.remove('is-valid', 'is-invalid');
            
            // Basic validation
            if (field.hasAttribute('required') && !value) {
                showFieldError(field, 'This field is required.');
                return false;
            }
            
            if (fieldName === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    showFieldError(field, 'Please enter a valid email address.');
                    return false;
                }
            }
            
            if (fieldName === 'phone' && value) {
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                    showFieldError(field, 'Please enter a valid phone number.');
                    return false;
                }
            }
            
            // If validation passes
            field.classList.add('is-valid');
            return true;
        }
        
        function showFieldError(field, message) {
            field.classList.add('is-invalid');
            const feedback = document.createElement('div');
            feedback.className = 'validation-feedback text-danger mt-1';
            feedback.innerHTML = `<small>${message}</small>`;
            field.parentNode.appendChild(feedback);
        }
    });
</script>
{% endblock %}
