{% extends 'base.html' %}

{% block title %}Change Password - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-key me-3"></i>
                            Change Password
                        </h1>
                        <p class="lead mb-0">Update your account password for security</p>
                    </div>
                    <div>
                        <a href="{% url 'employee_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Change Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Security Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Password Requirements:</strong>
                            <ul class="mb-0 mt-2">
                                <li>At least 8 characters long</li>
                                <li>Include uppercase and lowercase letters</li>
                                <li>Include at least one number</li>
                                <li>Include at least one special character</li>
                            </ul>
                        </div>

                        <form method="post" id="passwordChangeForm">
                            {% csrf_token %}
                            
                            <div class="mb-3">
                                <label for="{{ form.old_password.id_for_label }}" class="form-label">
                                    {{ form.old_password.label }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.old_password }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.old_password.id_for_label }}', 'toggleIcon1')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                                {% if form.old_password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.old_password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                                    {{ form.new_password1.label }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.new_password1 }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.new_password1.id_for_label }}', 'toggleIcon2')">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                                {% if form.new_password1.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.new_password1.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <!-- Password strength indicator -->
                                <div class="mt-2">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" id="passwordStrength" style="width: 0%;"></div>
                                    </div>
                                    <small id="strengthText" class="text-muted"></small>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                                    {{ form.new_password2.label }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    {{ form.new_password2 }}
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.new_password2.id_for_label }}', 'toggleIcon3')">
                                        <i class="fas fa-eye" id="toggleIcon3"></i>
                                    </button>
                                </div>
                                {% if form.new_password2.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.new_password2.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div id="passwordMatch" class="mt-1"></div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-warning btn-lg me-3">
                                    <i class="fas fa-key me-2"></i>
                                    Change Password
                                </button>
                                <a href="{% url 'employee_dashboard' %}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Security Tips Card -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-lightbulb me-2"></i>Security Tips
                        </h6>
                        <div class="row text-center">
                            <div class="col-md-4">
                                <i class="fas fa-lock fa-2x text-success mb-2"></i>
                                <h6>Strong Password</h6>
                                <small class="text-muted">Use a unique, complex password</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-sync-alt fa-2x text-primary mb-2"></i>
                                <h6>Regular Updates</h6>
                                <small class="text-muted">Change password periodically</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-user-secret fa-2x text-warning mb-2"></i>
                                <h6>Keep Private</h6>
                                <small class="text-muted">Never share your password</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword(fieldId, iconId) {
        const passwordField = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(iconId);
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    document.getElementById('passwordChangeForm').addEventListener('submit', function(e) {
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing...';
        submitBtn.disabled = true;
        
        // Re-enable button after a delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Password strength indicator
    document.getElementById('{{ form.new_password1.id_for_label }}').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('strengthText');
        
        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];
        
        strengthBar.style.width = (strength * 20) + '%';
        strengthBar.style.backgroundColor = strengthColors[strength] || strengthColors[0];
        strengthText.textContent = strengthLevels[strength] || strengthLevels[0];
        strengthText.style.color = strengthColors[strength] || strengthColors[0];
    });

    // Password match validation
    function checkPasswordMatch() {
        const password = document.getElementById('{{ form.new_password1.id_for_label }}').value;
        const confirmPassword = document.getElementById('{{ form.new_password2.id_for_label }}').value;
        const matchDiv = document.getElementById('passwordMatch');
        
        if (confirmPassword) {
            if (password === confirmPassword) {
                matchDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Passwords match</small>';
            } else {
                matchDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Passwords do not match</small>';
            }
        } else {
            matchDiv.innerHTML = '';
        }
    }

    document.getElementById('{{ form.new_password1.id_for_label }}').addEventListener('input', checkPasswordMatch);
    document.getElementById('{{ form.new_password2.id_for_label }}').addEventListener('input', checkPasswordMatch);
</script>
{% endblock %}
