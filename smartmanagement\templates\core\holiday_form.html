{% extends 'base.html' %}

{% block title %}{% if is_edit %}Edit Holiday{% else %}Add Holiday{% endif %} - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-check-input {
        width: 1.2em;
        height: 1.2em;
        margin-top: 0.25em;
        margin-right: 0.5em;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .form-check-label {
        font-weight: 500;
        color: #2d3436;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .text-danger {
        font-size: 0.875rem;
        margin-top: 5px;
    }
    
    .holiday-preview {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .preview-date {
        font-size: 1.2rem;
        font-weight: 600;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .preview-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 5px;
    }
    
    .preview-description {
        color: #636e72;
        font-style: italic;
    }
    
    .preview-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-top: 10px;
    }
    
    .badge-optional {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .badge-mandatory {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-{% if is_edit %}edit{% else %}plus{% endif %} me-3"></i>
                            {% if is_edit %}Edit Holiday{% else %}Add Holiday{% endif %}
                        </h1>
                        <p class="lead mb-0">{% if is_edit %}Update holiday information{% else %}Add a new public holiday{% endif %}</p>
                    </div>
                    <div>
                        <a href="{% url 'core:holiday_list' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Calendar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <h2>
                        <i class="fas fa-calendar-plus me-2"></i>
                        {% if is_edit %}Edit Holiday{% else %}Add New Holiday{% endif %}
                    </h2>
                    <p class="mb-0">{% if is_edit %}Update the holiday details below{% else %}Fill in the holiday details below{% endif %}</p>
                </div>
                
                <div class="p-4">
                    <!-- Holiday Preview -->
                    <div class="holiday-preview" id="holidayPreview" style="display: none;">
                        <h5><i class="fas fa-eye me-2"></i>Preview</h5>
                        <div class="preview-date" id="previewDate">-</div>
                        <div class="preview-name" id="previewName">Holiday Name</div>
                        <div class="preview-description" id="previewDescription">Holiday description will appear here</div>
                        <span class="preview-badge" id="previewBadge">Mandatory</span>
                    </div>
                    
                    <!-- Holiday Form -->
                    <form method="post" id="holidayForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.date.id_for_label }}" class="form-label">
                                        Holiday Date <span class="text-danger">*</span>
                                    </label>
                                    {{ form.date }}
                                    {% if form.date.errors %}
                                        <div class="text-danger mt-1">{{ form.date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        Holiday Name <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger mt-1">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger mt-1">{{ form.description.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">Optional description about the holiday</small>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                {{ form.is_optional }}
                                <label class="form-check-label" for="{{ form.is_optional.id_for_label }}">
                                    This is an optional holiday
                                </label>
                            </div>
                            {% if form.is_optional.errors %}
                                <div class="text-danger mt-1">{{ form.is_optional.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">Optional holidays can be taken at employee's discretion</small>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <a href="{% url 'core:holiday_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                {% if is_edit %}Update Holiday{% else %}Add Holiday{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('{{ form.date.id_for_label }}');
    const nameInput = document.getElementById('{{ form.name.id_for_label }}');
    const descriptionInput = document.getElementById('{{ form.description.id_for_label }}');
    const optionalInput = document.getElementById('{{ form.is_optional.id_for_label }}');
    
    const preview = document.getElementById('holidayPreview');
    const previewDate = document.getElementById('previewDate');
    const previewName = document.getElementById('previewName');
    const previewDescription = document.getElementById('previewDescription');
    const previewBadge = document.getElementById('previewBadge');
    
    function updatePreview() {
        const date = dateInput.value;
        const name = nameInput.value;
        const description = descriptionInput.value;
        const isOptional = optionalInput.checked;
        
        if (date || name) {
            preview.style.display = 'block';
            
            if (date) {
                const dateObj = new Date(date);
                previewDate.textContent = dateObj.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } else {
                previewDate.textContent = 'Select a date';
            }
            
            previewName.textContent = name || 'Holiday Name';
            previewDescription.textContent = description || 'No description provided';
            previewDescription.style.display = description ? 'block' : 'none';
            
            if (isOptional) {
                previewBadge.textContent = 'Optional';
                previewBadge.className = 'preview-badge badge-optional';
            } else {
                previewBadge.textContent = 'Mandatory';
                previewBadge.className = 'preview-badge badge-mandatory';
            }
        } else {
            preview.style.display = 'none';
        }
    }
    
    // Event listeners
    if (dateInput) dateInput.addEventListener('change', updatePreview);
    if (nameInput) nameInput.addEventListener('input', updatePreview);
    if (descriptionInput) descriptionInput.addEventListener('input', updatePreview);
    if (optionalInput) optionalInput.addEventListener('change', updatePreview);
    
    // Initial preview update
    updatePreview();
    
    // Form validation
    document.getElementById('holidayForm').addEventListener('submit', function(e) {
        const date = dateInput.value;
        const name = nameInput.value.trim();
        
        if (!date) {
            e.preventDefault();
            alert('Please select a holiday date.');
            dateInput.focus();
            return;
        }
        
        if (!name) {
            e.preventDefault();
            alert('Please enter a holiday name.');
            nameInput.focus();
            return;
        }
        
        // Check if date is in the past (only for new holidays)
        {% if not is_edit %}
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate < today) {
            if (!confirm('You are adding a holiday in the past. Are you sure you want to continue?')) {
                e.preventDefault();
                return;
            }
        }
        {% endif %}
    });
});
</script>
{% endblock %}
