{% extends 'base.html' %}

{% block title %}Leave & Attendance Dashboard - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .dashboard-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
        opacity: 0.9;
    }
    
    .quick-action-card {
        background: white;
        border-radius: 8px;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .quick-action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .quick-action-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
        color: var(--primary-color);
    }
    

    
    .attendance-chart {
        background: white;
        border-radius: 8px;
        padding: 1.25rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }

    .holiday-item {
        background: #f8fafc;
        border-left: 3px solid var(--warning-color);
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-radius: 0 6px 6px 0;
        font-size: 0.85rem;
    }

    .recent-leave-item {
        background: white;
        border-radius: 6px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 2px 6px rgba(0,0,0,0.04);
        border-left: 3px solid var(--primary-color);
        font-size: 0.85rem;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending { background: #fef3c7; color: #92400e; }
    .status-approved { background: #d1fae5; color: #065f46; }
    .status-rejected { background: #fee2e2; color: #991b1b; }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<section class="py-3" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="fas fa-calendar-check me-2"></i>
                            Welcome, {{ employee.full_name }}!
                        </h4>
                        <p class="mb-1 fs-6">Leave & Attendance Management Dashboard</p>
                        <small class="opacity-75">Employee ID: {{ employee.employee_code }} | {{ employee.department.name }} - {{ employee.designation.title }}</small>
                    </div>
                    <div>
                        <a href="{% url 'core:edit_employee_profile' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-user-edit me-1"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Stats -->
<div class="container mt-3">
    <div class="dashboard-stats">
        <div class="row">

            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ present_days }}</div>
                    <div class="stat-label">Present Days ({{ current_month }})</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_working_hours|floatformat:1 }}</div>
                    <div class="stat-label">Working Hours ({{ current_month }})</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ wfh_days }}</div>
                    <div class="stat-label">WFH Days ({{ current_month }})</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container">
    <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
    <div class="row">
        <div class="col-md-3 mb-2">
            <a href="{% url 'core:leave_request_create' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <h6 class="mb-1">Apply for Leave</h6>
                    <p class="text-muted mb-0 small">Submit a new leave request</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-2">
            <a href="{% url 'core:mark_attendance' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h6 class="mb-1">Mark Attendance</h6>
                    <p class="text-muted mb-0 small">Check-in/Check-out</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-2">
            <a href="{% url 'core:leave_request_list' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <h6 class="mb-1">My Leave Requests</h6>
                    <p class="text-muted mb-0 small">View leave history</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-2">
            <a href="{% url 'core:attendance_list' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h6 class="mb-1">My Attendance</h6>
                    <p class="text-muted mb-0 small">View attendance records</p>
                </div>
            </a>
        </div>
    </div>

    <!-- Second Row of Quick Actions -->
    <div class="row">
        <div class="col-md-3 mb-2">
            <a href="{% url 'core:edit_employee_profile' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h6 class="mb-1">Edit Profile</h6>
                    <p class="text-muted mb-0 small">Update your information</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-3">
    <!-- Compact Profile Section -->
    <div class="row mb-3">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ employee.full_name }}</h6>
                                    <small class="text-muted">{{ employee.department.name|default:"No Department" }} • {{ employee.designation.title|default:"No Designation" }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-success me-2">{{ employee.status }}</span>
                            <a href="{% url 'core:edit_employee_profile' %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Leave Requests -->
        <div class="col-lg-12">
            <h5 class="mb-3"><i class="fas fa-history me-2"></i>Recent Leave Requests</h5>
            {% for leave in recent_leaves %}
            <div class="recent-leave-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">{{ leave.leave_type.name }}</h6>
                        <small class="text-muted">{{ leave.start_date }} to {{ leave.end_date }}</small>
                        <p class="mb-1 mt-1">{{ leave.reason|truncatechars:50 }}</p>
                    </div>
                    <span class="status-badge status-{{ leave.status|lower }}">{{ leave.status }}</span>
                </div>
            </div>
            {% empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No leave requests found.
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Upcoming Holidays -->
    <div class="row mt-4">
        <div class="col-lg-12">
            <h5 class="mb-3"><i class="fas fa-calendar-alt me-2"></i>Upcoming Holidays</h5>
            <div class="row">
                {% for holiday in upcoming_holidays %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="holiday-item">
                        <h6 class="mb-1">{{ holiday.name }}</h6>
                        <small class="text-muted">{{ holiday.date|date:"F d, Y" }}</small>
                        {% if holiday.is_optional %}
                        <span class="badge bg-warning ms-2">Optional</span>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>No upcoming holidays.
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
