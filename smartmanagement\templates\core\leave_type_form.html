{% extends 'base.html' %}

{% block title %}{{ page_title }} - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-list-alt me-3"></i>{{ page_title }}
                        </h1>
                        <p class="lead mb-0">{{ page_description }}</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Leave Types
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list-alt me-2"></i>Leave Type Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {% csrf_token %}
                        
                        <!-- Leave Type Name -->
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <i class="fas fa-tag me-2"></i>Leave Type Name *
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enter a unique name for the leave type (e.g., Annual Leave, Sick Leave)</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>Description
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Provide a brief description of this leave type</div>
                        </div>

                        <!-- Max Days Per Year -->
                        <div class="mb-3">
                            <label for="{{ form.max_days_per_year.id_for_label }}" class="form-label">
                                <i class="fas fa-calendar-day me-2"></i>Maximum Days Per Year
                            </label>
                            {{ form.max_days_per_year }}
                            {% if form.max_days_per_year.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.max_days_per_year.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Maximum days allowed per year (0 for unlimited)</div>
                        </div>

                        <!-- Carry Forward Settings -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.is_carry_forward }}
                                        <label class="form-check-label" for="{{ form.is_carry_forward.id_for_label }}">
                                            <i class="fas fa-arrow-right me-2"></i>Allow Carry Forward
                                        </label>
                                    </div>
                                    {% if form.is_carry_forward.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_carry_forward.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Can unused leaves be carried forward to next year?</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.max_carry_forward_days.id_for_label }}" class="form-label">
                                        <i class="fas fa-forward me-2"></i>Max Carry Forward Days
                                    </label>
                                    {{ form.max_carry_forward_days }}
                                    {% if form.max_carry_forward_days.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.max_carry_forward_days.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Maximum days that can be carried forward (0 for unlimited)</div>
                                </div>
                            </div>
                        </div>

                        <!-- Active Status -->
                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    <i class="fas fa-check-circle me-2"></i>Active
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Is this leave type currently active and available for use?</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ form_action }} Leave Type
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information Card -->
            {% if leave_type %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Leave Type Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Created:</strong> 
                                <span class="text-muted">{{ leave_type.created_at|date:"M d, Y g:i A" }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Last Updated:</strong> 
                                <span class="text-muted">{{ leave_type.updated_at|date:"M d, Y g:i A" }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'core:leave_type_detail' leave_type.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-2"></i>View Leave Type Details
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.invalid-feedback {
    display: block !important;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle carry forward checkbox
    const carryForwardCheckbox = document.getElementById('{{ form.is_carry_forward.id_for_label }}');
    const maxCarryForwardInput = document.getElementById('{{ form.max_carry_forward_days.id_for_label }}');
    
    function toggleCarryForwardDays() {
        if (carryForwardCheckbox.checked) {
            maxCarryForwardInput.disabled = false;
            maxCarryForwardInput.parentElement.style.opacity = '1';
        } else {
            maxCarryForwardInput.disabled = true;
            maxCarryForwardInput.value = '0';
            maxCarryForwardInput.parentElement.style.opacity = '0.5';
        }
    }
    
    carryForwardCheckbox.addEventListener('change', toggleCarryForwardDays);
    toggleCarryForwardDays(); // Initial state
    
    // Form validation
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                this.classList.remove('is-invalid');
                if (this.value.trim() !== '') {
                    this.classList.add('is-valid');
                }
            }
        });
    });
});
</script>
{% endblock %}
