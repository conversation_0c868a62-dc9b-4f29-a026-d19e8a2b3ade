{% extends 'base.html' %}

{% block title %}Delete Department - {{ department.name }} - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-trash me-3"></i>Delete Department
                        </h1>
                        <p class="lead mb-0">Confirm department deletion</p>
                    </div>
                    <div>
                        <a href="{% url 'core:department_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Departments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Department Deletion
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone!
                    </div>
                    
                    <p class="mb-4">
                        You are about to delete the department <strong>"{{ department.name }}"</strong>. 
                        Please review the information below before proceeding.
                    </p>

                    <!-- Department Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Department Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> {{ department.name }}</p>
                                    <p><strong>Created:</strong> {{ department.created_at|date:"M d, Y" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Description:</strong> 
                                        {% if department.description %}
                                            {{ department.description|truncatewords:20 }}
                                        {% else %}
                                            <span class="text-muted">No description</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Last Updated:</strong> {{ department.updated_at|date:"M d, Y" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employee Impact -->
                    {% if active_employees > 0 %}
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-users me-2"></i>Cannot Delete Department</h6>
                            <p class="mb-2">
                                This department has <strong>{{ active_employees }} active employee{{ active_employees|pluralize }}</strong> 
                                and cannot be deleted.
                            </p>
                            <p class="mb-0">
                                <strong>Required Actions:</strong>
                            </p>
                            <ul class="mb-0">
                                <li>Reassign all employees to other departments, or</li>
                                <li>Change employee status to Inactive or Terminated</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:department_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Departments
                            </a>
                            <a href="{% url 'core:department_detail' department.pk %}" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>Manage Employees
                            </a>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Safe to Delete</h6>
                            <p class="mb-0">
                                This department has no active employees and can be safely deleted.
                            </p>
                        </div>

                        <!-- Deletion Impact -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">What will happen when you delete this department?</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>The department record will be permanently removed</li>
                                    <li>Any historical employee assignments will remain in the system</li>
                                    <li>Reports and analytics will show this department as "Deleted"</li>
                                    <li>This action cannot be undone</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="POST" id="deleteForm">
                            {% csrf_token %}
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    I understand that this action cannot be undone and want to proceed with deleting 
                                    the department "{{ department.name }}".
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{% url 'core:department_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="fas fa-trash me-2"></i>Delete Department
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>

            <!-- Additional Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Alternative Actions
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Instead of deleting, you might want to:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{% url 'core:department_edit' department.pk %}" class="btn btn-outline-primary btn-sm mb-2">
                                <i class="fas fa-edit me-2"></i>Edit Department Information
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'core:department_detail' department.pk %}" class="btn btn-outline-info btn-sm mb-2">
                                <i class="fas fa-eye me-2"></i>View Department Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.border-danger {
    border-color: #dc3545 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    if (confirmCheckbox && deleteButton) {
        confirmCheckbox.addEventListener('change', function() {
            deleteButton.disabled = !this.checked;
        });
        
        deleteForm.addEventListener('submit', function(e) {
            if (!confirmCheckbox.checked) {
                e.preventDefault();
                alert('Please confirm that you want to delete this department.');
                return false;
            }
            
            // Double confirmation
            const confirmed = confirm(
                'Are you absolutely sure you want to delete the department "{{ department.name }}"? ' +
                'This action cannot be undone.'
            );
            
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>
{% endblock %}
