{% extends 'base.html' %}

{% block title %}{{ leave_type.name }} - Leave Type Details - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-list-alt me-3"></i>{{ leave_type.name }}
                        </h1>
                        <p class="lead mb-0">Leave Type Details & Usage Statistics</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Leave Types
                        </a>
                        <a href="{% url 'core:leave_type_edit' leave_type.pk %}" class="btn btn-light">
                            <i class="fas fa-edit me-2"></i>Edit Leave Type
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Leave Type Information -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Leave Type Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Leave Type Name</h6>
                            <p class="text-muted">{{ leave_type.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Status</h6>
                            {% if leave_type.is_active %}
                                <span class="badge bg-success fs-6">Active</span>
                            {% else %}
                                <span class="badge bg-danger fs-6">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6>Description</h6>
                            {% if leave_type.description %}
                                <p class="text-muted">{{ leave_type.description }}</p>
                            {% else %}
                                <p class="text-muted fst-italic">No description provided</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Maximum Days Per Year</h6>
                            {% if leave_type.max_days_per_year > 0 %}
                                <p class="text-muted">{{ leave_type.max_days_per_year }} days</p>
                            {% else %}
                                <p class="text-muted">Unlimited</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>Carry Forward</h6>
                            {% if leave_type.is_carry_forward %}
                                <p class="text-success">
                                    <i class="fas fa-check me-1"></i>Allowed
                                    {% if leave_type.max_carry_forward_days > 0 %}
                                        (Max: {{ leave_type.max_carry_forward_days }} days)
                                    {% else %}
                                        (Unlimited)
                                    {% endif %}
                                </p>
                            {% else %}
                                <p class="text-danger">
                                    <i class="fas fa-times me-1"></i>Not Allowed
                                </p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Created Date</h6>
                            <p class="text-muted">{{ leave_type.created_at|date:"M d, Y g:i A" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Last Updated</h6>
                            <p class="text-muted">{{ leave_type.updated_at|date:"M d, Y g:i A" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Usage Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Leave Requests</span>
                            <span class="badge bg-primary">{{ total_leave_requests }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Pending Requests</span>
                            <span class="badge bg-warning">{{ pending_requests }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Approved Requests</span>
                            <span class="badge bg-success">{{ approved_requests }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Rejected Requests</span>
                            <span class="badge bg-danger">{{ rejected_requests }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span>Leave Balances ({{ current_year }})</span>
                            <span class="badge bg-info">{{ current_year_balances.count }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'core:leave_type_edit' leave_type.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Edit Leave Type
                        </a>
                        {% if leave_type.is_active %}
                        <button class="btn btn-outline-warning btn-sm" onclick="toggleStatus(false)">
                            <i class="fas fa-pause me-2"></i>Deactivate
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success btn-sm" onclick="toggleStatus(true)">
                            <i class="fas fa-play me-2"></i>Activate
                        </button>
                        {% endif %}
                        {% if total_leave_requests == 0 and total_balances == 0 %}
                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash me-2"></i>Delete Leave Type
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Leave Requests -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-calendar-times me-2"></i>Recent Leave Requests
            </h5>
            <span class="badge bg-primary">{{ recent_requests|length }} recent request{{ recent_requests|length|pluralize }}</span>
        </div>
        
        <div class="card-body p-0">
            {% if recent_requests %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Employee</th>
                                <th>Leave Period</th>
                                <th>Days</th>
                                <th>Status</th>
                                <th>Applied On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in recent_requests %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ request.employee.user.get_full_name|default:request.employee.user.username }}</h6>
                                            <small class="text-muted">{{ request.employee.employee_code }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ request.start_date|date:"M d" }} - {{ request.end_date|date:"M d, Y" }}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ request.total_days }} day{{ request.total_days|pluralize }}</span>
                                </td>
                                <td>
                                    {% if request.status == 'Pending' %}
                                        <span class="badge bg-warning">{{ request.status }}</span>
                                    {% elif request.status == 'Approved' %}
                                        <span class="badge bg-success">{{ request.status }}</span>
                                    {% elif request.status == 'Rejected' %}
                                        <span class="badge bg-danger">{{ request.status }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ request.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ request.applied_on|date:"M d, Y" }}</small>
                                </td>
                                <td>
                                    <a href="{% url 'core:leave_request_detail' request.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No leave requests found</h5>
                    <p class="text-muted">No employees have requested this type of leave yet.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if total_leave_requests == 0 and total_balances == 0 %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Leave Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the leave type <strong>"{{ leave_type.name }}"</strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{% url 'core:leave_type_delete' leave_type.pk %}" class="btn btn-danger">Delete Leave Type</a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
</style>

<script>
function toggleStatus(activate) {
    const action = activate ? 'activate' : 'deactivate';
    const confirmMessage = `Are you sure you want to ${action} the leave type "${leave_type.name}"?`;
    
    if (confirm(confirmMessage)) {
        // Here you would typically make an AJAX call to update the status
        // For now, we'll redirect to the edit page
        window.location.href = "{% url 'core:leave_type_edit' leave_type.pk %}";
    }
}
</script>
{% endblock %}
