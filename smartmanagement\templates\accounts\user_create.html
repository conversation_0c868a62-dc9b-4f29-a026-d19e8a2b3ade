{% extends 'base.html' %}

{% block title %}Create New User - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-user-plus me-3"></i>
                            Create New User
                        </h1>
                        <p class="lead mb-0">Add a new user to the system</p>
                    </div>
                    <a href="{% url 'user_list' %}" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Create Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" id="createUserForm">
                            {% csrf_token %}
                            
                            <!-- Personal Information -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>First Name *
                                    </label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.first_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>Last Name *
                                    </label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.last_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.username.id_for_label }}" class="form-label">
                                        <i class="fas fa-at me-2"></i>Username *
                                    </label>
                                    {{ form.username }}
                                    {% if form.username.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.username.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">Username must be unique</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address *
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.role.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tag me-2"></i>Role *
                                    </label>
                                    {{ form.role }}
                                    {% if form.role.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.role.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-2"></i>Phone Number
                                    </label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.phone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Date of Birth
                                </label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.date_of_birth.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.address.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Section -->
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-lock me-2"></i>Password Setup
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.password.id_for_label }}" class="form-label">Password *</label>
                                            <div class="input-group">
                                                {{ form.password }}
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.password.id_for_label }}', 'toggleIcon1')">
                                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                                </button>
                                            </div>
                                            {% if form.password.errors %}
                                                <div class="text-danger mt-1">
                                                    {% for error in form.password.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <div class="mt-2">
                                                <div class="progress" style="height: 5px;">
                                                    <div class="progress-bar" id="passwordStrength" style="width: 0%"></div>
                                                </div>
                                                <small id="strengthText" class="text-muted"></small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.confirm_password.id_for_label }}" class="form-label">Confirm Password *</label>
                                            <div class="input-group">
                                                {{ form.confirm_password }}
                                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.confirm_password.id_for_label }}', 'toggleIcon2')">
                                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                                </button>
                                            </div>
                                            {% if form.confirm_password.errors %}
                                                <div class="text-danger mt-1">
                                                    {% for error in form.confirm_password.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <div id="passwordMatch" class="mt-1"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="{% url 'user_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-user-plus me-2"></i>Create User
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Help & Guidelines
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Password Requirements:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>At least 8 characters long</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Include uppercase and lowercase letters</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Include at least one number</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Include special characters</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>User Roles:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-crown text-warning me-2"></i><strong>Admin:</strong> Full system access</li>
                                    <li><i class="fas fa-user text-info me-2"></i><strong>Employee:</strong> Limited access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword(fieldId, iconId) {
        const passwordField = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(iconId);
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    document.getElementById('createUserForm').addEventListener('submit', function(e) {
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
        submitBtn.disabled = true;
        
        // Re-enable button after a delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Password strength indicator
    document.getElementById('{{ form.password.id_for_label }}').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('strengthText');
        
        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];
        
        strengthBar.style.width = (strength * 20) + '%';
        strengthBar.style.backgroundColor = strengthColors[strength - 1] || '#e5e7eb';
        strengthText.textContent = strengthLevels[strength - 1] || '';
        strengthText.style.color = strengthColors[strength - 1] || '#6b7280';
    });

    // Password match validation
    function checkPasswordMatch() {
        const password = document.getElementById('{{ form.password.id_for_label }}').value;
        const confirmPassword = document.getElementById('{{ form.confirm_password.id_for_label }}').value;
        const matchDiv = document.getElementById('passwordMatch');
        
        if (confirmPassword) {
            if (password === confirmPassword) {
                matchDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Passwords match</small>';
            } else {
                matchDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Passwords do not match</small>';
            }
        } else {
            matchDiv.innerHTML = '';
        }
    }

    document.getElementById('{{ form.password.id_for_label }}').addEventListener('input', checkPasswordMatch);
    document.getElementById('{{ form.confirm_password.id_for_label }}').addEventListener('input', checkPasswordMatch);
</script>
{% endblock %}
