{% extends 'base.html' %}

{% block title %}Admin Dashboard - Leave & Attendance Management{% endblock %}

{% block extra_css %}
<style>
    .admin-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        backdrop-filter: blur(10px);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .management-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        height: 100%;
    }
    
    .management-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .management-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }
    
    .pending-requests {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .request-item {
        background: #f8fafc;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid var(--warning-color);
    }
    
    .department-stat {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending { background: #fef3c7; color: #92400e; }
    .status-approved { background: #d1fae5; color: #065f46; }
    .status-rejected { background: #fee2e2; color: #991b1b; }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-cogs me-3"></i>
                            Admin Dashboard
                        </h1>
                        <p class="lead mb-0">Leave & Attendance Management System</p>
                    </div>
                    <div>
                        <a href="{% url 'admin_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Main Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Admin Stats -->
<div class="container mt-4">
    <div class="admin-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ total_employees }}</div>
                    <div class="stat-label">Total Employees</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ pending_leaves }}</div>
                    <div class="stat-label">Pending Leave Requests</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ present_today }}</div>
                    <div class="stat-label">Present Today</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number">{{ absent_today }}</div>
                    <div class="stat-label">Absent Today</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Management Modules -->
<div class="container">
    <h3 class="mb-4"><i class="fas fa-tools me-2"></i>Management Modules</h3>
    <div class="row">
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:employee_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5>Employee Management</h5>
                    <p class="text-muted mb-0">Manage employee profiles and details</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:leave_request_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <h5>Leave Management</h5>
                    <p class="text-muted mb-0">Approve/reject leave requests</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:attendance_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5>Attendance Management</h5>
                    <p class="text-muted mb-0">Track and manage attendance</p>
                </div>
            </a>
        </div>

    </div>
    
    <!-- Configuration Modules -->
    <h4 class="mb-3 mt-4"><i class="fas fa-cog me-2"></i>Configuration</h4>
    <div class="row">
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:department_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <h5>Departments</h5>
                    <p class="text-muted mb-0">Manage departments</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:designation_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h5>Designations</h5>
                    <p class="text-muted mb-0">Manage job positions</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:leave_type_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-list-alt"></i>
                    </div>
                    <h5>Leave Types</h5>
                    <p class="text-muted mb-0">Configure leave types</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:holiday_list' %}" class="text-decoration-none">
                <div class="management-card">
                    <div class="management-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5>Holiday Calendar</h5>
                    <p class="text-muted mb-0">Manage public holidays</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="container mt-4">
    <div class="row">
        <!-- Pending Leave Requests -->
        <div class="col-lg-8">
            <div class="pending-requests">
                <h4 class="mb-3"><i class="fas fa-clock me-2"></i>Recent Leave Requests</h4>
                {% for request in recent_leave_requests %}
                <div class="request-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">{{ request.employee.full_name }}</h6>
                            <small class="text-muted">{{ request.leave_type.name }} | {{ request.start_date }} to {{ request.end_date }}</small>
                            <p class="mb-1 mt-1">{{ request.reason|truncatechars:60 }}</p>
                            <small class="text-muted">Applied on: {{ request.applied_on|date:"M d, Y" }}</small>
                        </div>
                        <div class="text-end">
                            <span class="status-badge status-{{ request.status|lower }}">{{ request.status }}</span>
                            {% if request.status == 'Pending' %}
                            <div class="mt-2">
                                <a href="{% url 'core:leave_request_approve' request.pk %}" class="btn btn-sm btn-success me-1">
                                    <i class="fas fa-check"></i>
                                </a>
                                <a href="{% url 'core:leave_request_detail' request.pk %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No recent leave requests.
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Department Statistics -->
        <div class="col-lg-4">
            <div class="pending-requests">
                <h4 class="mb-3"><i class="fas fa-building me-2"></i>Department Overview</h4>
                {% for dept in departments %}
                <div class="department-stat">
                    <div>
                        <h6 class="mb-0">{{ dept.name }}</h6>
                        <small class="text-muted">{{ dept.employee_count }} employees</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary">{{ dept.employee_count }}</span>
                    </div>
                </div>
                {% empty %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No departments configured.
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
