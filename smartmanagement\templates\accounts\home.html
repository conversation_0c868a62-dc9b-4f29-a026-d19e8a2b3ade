{% extends 'base.html' %}

{% block title %}Home - Smart Management System{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="hero-title">
                    <i class="fas fa-users me-3"></i>
                    Smart User Management System
                </h1>
                <p class="hero-subtitle">
                    Comprehensive user management and administration platform
                </p>
                {% if not user.is_authenticated %}
                <div class="mt-4">
                    <a href="{% url 'signup' %}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-user-plus me-2"></i>Get Started
                    </a>
                    <a href="{% url 'login' %}" class="btn btn-secondary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                </div>
                {% else %}
                <div class="mt-4">
                    {% if user.role == 'admin' %}
                    <a href="{% url 'admin_dashboard' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                    </a>
                    {% else %}
                    <a href="{% url 'employee_dashboard' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-user me-2"></i>Go to Dashboard
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-12">
                <h2 class="animate-on-scroll">Why Choose Our User Management System?</h2>
                <p class="lead animate-on-scroll">Powerful user management features for modern organizations</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <h4>Complete User Management</h4>
                        <p class="text-muted">Create, edit, delete, and manage users with comprehensive admin controls.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h4>Role-Based Access</h4>
                        <p class="text-muted">Admin and Employee roles with appropriate permissions and access levels.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-trash-restore"></i>
                        </div>
                        <h4>Soft Delete & Restore</h4>
                        <p class="text-muted">Safely delete users with the ability to restore them when needed.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4>Advanced Search & Filter</h4>
                        <p class="text-muted">Find users quickly with powerful search and filtering capabilities.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>User Analytics</h4>
                        <p class="text-muted">Track user statistics, activity, and system usage with detailed reports.</p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card feature-card animate-on-scroll">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Responsive Design</h4>
                        <p class="text-muted">Beautiful, modern interface that works perfectly on all devices.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
{% if not user.is_authenticated %}
<section class="py-5" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-12">
                <h2 class="text-white mb-4">Ready to Manage Users Efficiently?</h2>
                <p class="text-white mb-4 lead">Join organizations using our comprehensive user management system</p>
                <a href="{% url 'signup' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Create Your Account
                </a>
            </div>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
