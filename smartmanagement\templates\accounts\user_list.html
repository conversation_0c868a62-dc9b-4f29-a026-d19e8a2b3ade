{% extends 'base.html' %}

{% block title %}User Management - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-users me-3"></i>
                            User Management
                        </h1>
                        <p class="lead mb-0">Manage all registered users - Total: {{ total_users }}</p>
                    </div>
                    <a href="{% url 'user_create' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-4">
    <div class="container">
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Search by name, username, or email">
                    </div>
                    <div class="col-md-3">
                        <label for="role" class="form-label">Filter by Role</label>
                        <select class="form-control" id="role" name="role">
                            <option value="">All Roles</option>
                            <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>Admin</option>
                            <option value="employee" {% if role_filter == 'employee' %}selected{% endif %}>Employee</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Filter by Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">Active Users</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                            <option value="deleted" {% if status_filter == 'deleted' %}selected{% endif %}>Deleted</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Users Table -->
<section class="pb-5">
    <div class="container">
        <div class="card">
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Avatar</th>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in page_obj %}
                            <tr class="{% if user.is_deleted %}table-danger{% elif not user.is_active %}table-warning{% endif %}">
                                <td>
                                    <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        {% if user.first_name %}
                                            {{ user.first_name.0 }}{{ user.last_name.0|default:'' }}
                                        {% else %}
                                            {{ user.username.0|upper }}
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ user.first_name }} {{ user.last_name }}</strong>
                                        {% if user.is_deleted %}
                                            <span class="badge bg-danger ms-2">Deleted</span>
                                        {% elif not user.is_active %}
                                            <span class="badge bg-warning ms-2">Inactive</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    <span class="badge {% if user.role == 'admin' %}bg-success{% else %}bg-info{% endif %}">
                                        {{ user.get_role_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if user.is_deleted %}
                                        <span class="badge bg-danger">Deleted</span>
                                    {% elif user.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-warning">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'user_detail' user.id %}" class="btn btn-sm btn-outline-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if not user.is_deleted %}
                                            <a href="{% url 'user_edit' user.id %}" class="btn btn-sm btn-outline-primary" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-warning" onclick="toggleUserStatus({{ user.id }})" title="Toggle Status">
                                                <i class="fas {% if user.is_active %}fa-user-slash{% else %}fa-user-check{% endif %}"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }}, '{{ user.username }}')" title="Delete User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-sm btn-outline-success" onclick="restoreUser({{ user.id }}, '{{ user.username }}')" title="Restore User">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="User pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4>No users found</h4>
                    <p class="text-muted">Try adjusting your search criteria or add a new user.</p>
                    <a href="{% url 'user_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>Add First User
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function deleteUser(userId, username) {
        if (confirm(`Are you sure you want to delete user "${username}"? This action can be undone later.`)) {
            fetch(`/account/users/${userId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the user.');
            });
        }
    }

    function restoreUser(userId, username) {
        if (confirm(`Are you sure you want to restore user "${username}"?`)) {
            fetch(`/account/users/${userId}/restore/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while restoring the user.');
            });
        }
    }

    function toggleUserStatus(userId) {
        fetch(`/account/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating user status.');
        });
    }

    // Add CSRF token to all forms
    document.addEventListener('DOMContentLoaded', function() {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (!csrfToken) {
            const token = document.createElement('input');
            token.type = 'hidden';
            token.name = 'csrfmiddlewaretoken';
            token.value = '{{ csrf_token }}';
            document.body.appendChild(token);
        }
    });
</script>
{% endblock %}
