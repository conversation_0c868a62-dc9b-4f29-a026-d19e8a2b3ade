{% extends 'base.html' %}

{% block title %}{{ user_detail.username }} - User Details{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-user me-3"></i>
                            User Details
                        </h1>
                        <p class="lead mb-0">{{ user_detail.first_name }} {{ user_detail.last_name }}</p>
                    </div>
                    <div>
                        <a href="{% url 'user_list' %}" class="btn btn-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Users
                        </a>
                        {% if not user_detail.is_deleted %}
                        <a href="{% url 'user_edit' user_detail.id %}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- User Information -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Profile Card -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="avatar bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                             style="width: 100px; height: 100px; font-size: 2rem;">
                            {% if user_detail.first_name %}
                                {{ user_detail.first_name.0 }}{{ user_detail.last_name.0|default:'' }}
                            {% else %}
                                {{ user_detail.username.0|upper }}
                            {% endif %}
                        </div>
                        <h4>{{ user_detail.first_name }} {{ user_detail.last_name }}</h4>
                        <p class="text-muted">@{{ user_detail.username }}</p>
                        
                        <div class="mb-3">
                            <span class="badge {% if user_detail.role == 'admin' %}bg-success{% else %}bg-info{% endif %} fs-6">
                                {{ user_detail.get_role_display }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            {% if user_detail.is_deleted %}
                                <span class="badge bg-danger fs-6">Deleted Account</span>
                            {% elif user_detail.is_active %}
                                <span class="badge bg-success fs-6">Active</span>
                            {% else %}
                                <span class="badge bg-warning fs-6">Inactive</span>
                            {% endif %}
                        </div>
                        
                        {% if not user_detail.is_deleted %}
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-warning" onclick="toggleUserStatus({{ user_detail.id }})">
                                <i class="fas {% if user_detail.is_active %}fa-user-slash{% else %}fa-user-check{% endif %} me-2"></i>
                                {% if user_detail.is_active %}Deactivate{% else %}Activate{% endif %}
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteUser({{ user_detail.id }}, '{{ user_detail.username }}')">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </button>
                        </div>
                        {% else %}
                        <div class="d-grid">
                            <button class="btn btn-outline-success" onclick="restoreUser({{ user_detail.id }}, '{{ user_detail.username }}')">
                                <i class="fas fa-undo me-2"></i>Restore User
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Detailed Information -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Personal Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">First Name</label>
                                <p class="fw-bold">{{ user_detail.first_name|default:"Not provided" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Last Name</label>
                                <p class="fw-bold">{{ user_detail.last_name|default:"Not provided" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Username</label>
                                <p class="fw-bold">{{ user_detail.username }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Email</label>
                                <p class="fw-bold">{{ user_detail.email }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Phone</label>
                                <p class="fw-bold">{{ user_detail.phone|default:"Not provided" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Date of Birth</label>
                                <p class="fw-bold">{{ user_detail.date_of_birth|default:"Not provided" }}</p>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Address</label>
                                <p class="fw-bold">{{ user_detail.address|default:"Not provided" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Account Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>Account Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Role</label>
                                <p class="fw-bold">
                                    <span class="badge {% if user_detail.role == 'admin' %}bg-success{% else %}bg-info{% endif %}">
                                        {{ user_detail.get_role_display }}
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Account Status</label>
                                <p class="fw-bold">
                                    {% if user_detail.is_deleted %}
                                        <span class="badge bg-danger">Deleted</span>
                                    {% elif user_detail.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-warning">Inactive</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Date Joined</label>
                                <p class="fw-bold">{{ user_detail.created_at|date:"F d, Y g:i A" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Last Updated</label>
                                <p class="fw-bold">{{ user_detail.updated_at|date:"F d, Y g:i A" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Last Login</label>
                                <p class="fw-bold">{{ user_detail.last_login|date:"F d, Y g:i A"|default:"Never" }}</p>
                            </div>
                            {% if user_detail.is_deleted %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Deleted At</label>
                                <p class="fw-bold text-danger">{{ user_detail.deleted_at|date:"F d, Y g:i A" }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Permissions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Permissions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" {% if user_detail.is_staff %}checked{% endif %} disabled>
                                    <label class="form-check-label">Staff Status</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" {% if user_detail.is_superuser %}checked{% endif %} disabled>
                                    <label class="form-check-label">Superuser</label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" {% if user_detail.is_active %}checked{% endif %} disabled>
                                    <label class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function deleteUser(userId, username) {
        if (confirm(`Are you sure you want to delete user "${username}"? This action can be undone later.`)) {
            fetch(`/account/users/${userId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '{% url "user_list" %}';
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the user.');
            });
        }
    }

    function restoreUser(userId, username) {
        if (confirm(`Are you sure you want to restore user "${username}"?`)) {
            fetch(`/account/users/${userId}/restore/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while restoring the user.');
            });
        }
    }

    function toggleUserStatus(userId) {
        fetch(`/account/users/${userId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating user status.');
        });
    }
</script>
{% endblock %}
