{% extends 'base.html' %}

{% block title %}Holiday Calendar - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .holiday-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .holiday-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .holiday-date {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px 20px 20px 20px;
        text-align: center;
        position: relative;
    }
    
    .holiday-date .day {
        font-size: 2.5rem;
        font-weight: bold;
        line-height: 1;
    }
    
    .holiday-date .month {
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-top: 5px;
    }
    
    .holiday-date .year {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .holiday-info {
        padding: 20px;
    }
    
    .holiday-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 8px;
    }
    
    .holiday-description {
        color: #636e72;
        font-size: 0.95rem;
        line-height: 1.4;
    }
    
    .holiday-badge {
        position: absolute;
        top: 5px;
        right: 5px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        z-index: 10;
    }
    
    .badge-optional {
        background: rgba(255, 193, 7, 0.2);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .badge-mandatory {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .upcoming-holidays {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .upcoming-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .upcoming-item:last-child {
        border-bottom: none;
    }
    
    .upcoming-date {
        background: #667eea;
        color: white;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 0.85rem;
        font-weight: 600;
        margin-right: 15px;
        min-width: 80px;
        text-align: center;
    }
    
    .upcoming-name {
        font-weight: 600;
        color: #2d3436;
    }
    
    .admin-actions {
        display: flex;
        gap: 8px;
        margin-top: 15px;
    }
    
    .btn-sm {
        padding: 6px 12px;
        font-size: 0.875rem;
        border-radius: 6px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #636e72;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="container mt-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% if is_admin %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:dashboard' %}{% endif %}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Holiday Calendar</li>
        </ol>
    </nav>
</div>

<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-calendar-alt me-3"></i>Holiday Calendar {{ current_year }}
                        </h1>
                        <p class="lead mb-0">Manage and view public holidays</p>
                    </div>
                    <div>
                        {% if is_admin %}
                        <a href="{% url 'core:holiday_create' %}" class="btn btn-light">
                            <i class="fas fa-plus me-2"></i>Add Holiday
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Filters and Search -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">Year</label>
                <select name="year" class="form-select">
                    {% for year_option in available_years %}
                    <option value="{{ year_option }}" {% if year_option == current_year %}selected{% endif %}>
                        {{ year_option }}
                    </option>
                    {% empty %}
                    <option value="{{ current_year }}">{{ current_year }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Type</label>
                <select name="type" class="form-select">
                    <option value="">All Holidays</option>
                    <option value="mandatory" {% if holiday_type == 'mandatory' %}selected{% endif %}>Mandatory</option>
                    <option value="optional" {% if holiday_type == 'optional' %}selected{% endif %}>Optional</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Search</label>
                <input type="text" name="search" class="form-control" placeholder="Search holidays..." value="{{ search_query }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
            </div>
        </form>
    </div>
    
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Stats -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">{{ total_holidays }}</div>
                        <div>Total Holidays</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">{{ page_obj.object_list|length }}</div>
                        <div>This Year</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">{{ upcoming_holidays|length }}</div>
                        <div>Upcoming</div>
                    </div>
                </div>
            </div>
            
            <!-- Holiday Cards -->
            {% for holiday in page_obj %}
            <div class="holiday-card">
                <div class="row g-0">
                    <div class="col-md-3">
                        <div class="holiday-date">
                            {% if holiday.is_optional %}
                            <div class="holiday-badge badge-optional">Optional</div>
                            {% else %}
                            <div class="holiday-badge badge-mandatory">Mandatory</div>
                            {% endif %}
                            <div class="day">{{ holiday.date.day }}</div>
                            <div class="month">{{ holiday.date|date:"M" }}</div>
                            <div class="year">{{ holiday.date.year }}</div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="holiday-info">
                            <div class="holiday-name">{{ holiday.name }}</div>
                            {% if holiday.description %}
                            <div class="holiday-description">{{ holiday.description }}</div>
                            {% endif %}
                            
                            {% if is_admin %}
                            <div class="admin-actions">
                                <a href="{% url 'core:holiday_edit' holiday.pk %}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                                <a href="{% url 'core:holiday_delete' holiday.pk %}" 
                                   class="btn btn-outline-danger btn-sm"
                                   onclick="return confirm('Are you sure you want to delete this holiday?')">
                                    <i class="fas fa-trash me-1"></i>Delete
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h4>No Holidays Found</h4>
                <p>No holidays found for the selected criteria.</p>
                {% if is_admin %}
                <a href="{% url 'core:holiday_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add First Holiday
                </a>
                {% endif %}
            </div>
            {% endfor %}
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Holiday pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if holiday_type %}&type={{ holiday_type }}{% endif %}{% if current_year %}&year={{ current_year }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if holiday_type %}&type={{ holiday_type }}{% endif %}{% if current_year %}&year={{ current_year }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if holiday_type %}&type={{ holiday_type }}{% endif %}{% if current_year %}&year={{ current_year }}{% endif %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="upcoming-holidays">
                <h5 class="mb-3">
                    <i class="fas fa-clock me-2"></i>Upcoming Holidays
                </h5>
                {% for holiday in upcoming_holidays %}
                <div class="upcoming-item">
                    <div class="upcoming-date">{{ holiday.date|date:"M d" }}</div>
                    <div>
                        <div class="upcoming-name">{{ holiday.name }}</div>
                        {% if holiday.is_optional %}
                        <small class="text-warning">Optional</small>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-calendar-check fa-2x mb-2"></i>
                    <p class="mb-0">No upcoming holidays</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
