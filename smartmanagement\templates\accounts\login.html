{% extends 'base.html' %}

{% block title %}Login - Smart Management System{% endblock %}

{% block content %}
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="card animate-on-scroll">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-sign-in-alt fa-3x text-primary mb-3"></i>
                            <h2 class="card-title">Welcome Back!</h2>
                            <p class="text-muted">Please sign in to your account</p>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <!-- Error Summary -->
                        <div id="errorSummary" class="error-summary" style="display: none;">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul id="errorList"></ul>
                        </div>

                        <form method="post" id="loginForm">
                            {% csrf_token %}

                            <div class="mb-2">
                                <label for="{{ form.username.id_for_label }}" class="form-label small required-field">
                                    <i class="fas fa-user me-1"></i>Username
                                </label>
                                <input type="text"
                                       class="form-control form-control-sm"
                                       id="{{ form.username.id_for_label }}"
                                       name="{{ form.username.name }}"
                                       placeholder="Username"
                                       required>
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-2">
                                <label for="{{ form.password.id_for_label }}" class="form-label small required-field">
                                    <i class="fas fa-lock me-1"></i>Password
                                </label>
                                <div class="input-group input-group-sm">
                                    <input type="password"
                                           class="form-control"
                                           id="{{ form.password.id_for_label }}"
                                           name="{{ form.password.name }}"
                                           placeholder="Password"
                                           required>
                                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">Don't have an account?
                                    <a href="{% url 'signup' %}" class="text-primary text-decoration-none">
                                        <strong>Sign up here</strong>
                                    </a>
                                </p>
                            </div>
                        </form>

                        <div class="loading">
                            <div class="spinner"></div>
                            <p class="mt-2">Signing you in...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword() {
        const passwordField = document.getElementById('{{ form.password.id_for_label }}');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    document.getElementById('loginForm').addEventListener('submit', function(e) {
        if (!validateLoginForm()) {
            e.preventDefault();
            return false;
        }
        showLoading('loginForm');
    });

    // Login form validation function
    function validateLoginForm() {
        const errors = [];
        const form = document.getElementById('loginForm');
        const errorSummary = document.getElementById('errorSummary');
        const errorList = document.getElementById('errorList');

        // Clear previous errors
        errorList.innerHTML = '';
        errorSummary.style.display = 'none';

        // Remove previous validation classes
        form.querySelectorAll('.form-control').forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });

        // Validate username
        const usernameInput = document.getElementById('{{ form.username.id_for_label }}');
        if (!usernameInput.value.trim()) {
            errors.push('Username is required');
            usernameInput.classList.add('is-invalid');
        } else {
            usernameInput.classList.add('is-valid');
        }

        // Validate password
        const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
        if (!passwordInput.value.trim()) {
            errors.push('Password is required');
            passwordInput.classList.add('is-invalid');
        } else {
            passwordInput.classList.add('is-valid');
        }

        // Show errors if any
        if (errors.length > 0) {
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
            errorSummary.style.display = 'block';
            errorSummary.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return false;
        }

        return true;
    }

    // Real-time validation for login form
    document.addEventListener('DOMContentLoaded', function() {
        const requiredInputs = document.querySelectorAll('input[required]');

        requiredInputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });

            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid') && this.value.trim()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    });
</script>
{% endblock %}
