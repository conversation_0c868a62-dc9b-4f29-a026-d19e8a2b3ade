{% extends 'base.html' %}

{% block title %}Delete Leave Type - {{ leave_type.name }} - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-trash me-3"></i>Delete Leave Type
                        </h1>
                        <p class="lead mb-0">Confirm leave type deletion</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_type_list' %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Leave Types
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Leave Type Deletion
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone!
                    </div>
                    
                    <p class="mb-4">
                        You are about to delete the leave type <strong>"{{ leave_type.name }}"</strong>. 
                        Please review the information below before proceeding.
                    </p>

                    <!-- Leave Type Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Leave Type Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> {{ leave_type.name }}</p>
                                    <p><strong>Status:</strong> 
                                        {% if leave_type.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Max Days/Year:</strong> 
                                        {% if leave_type.max_days_per_year > 0 %}
                                            {{ leave_type.max_days_per_year }} days
                                        {% else %}
                                            Unlimited
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Carry Forward:</strong> 
                                        {% if leave_type.is_carry_forward %}
                                            <span class="text-success">Allowed</span>
                                            {% if leave_type.max_carry_forward_days > 0 %}
                                                (Max: {{ leave_type.max_carry_forward_days }} days)
                                            {% endif %}
                                        {% else %}
                                            <span class="text-danger">Not Allowed</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Created:</strong> {{ leave_type.created_at|date:"M d, Y" }}</p>
                                    <p><strong>Last Updated:</strong> {{ leave_type.updated_at|date:"M d, Y" }}</p>
                                </div>
                            </div>
                            {% if leave_type.description %}
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>Description:</strong> {{ leave_type.description }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Usage Impact -->
                    {% if leave_requests_count > 0 or leave_balances_count > 0 %}
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-ban me-2"></i>Cannot Delete Leave Type</h6>
                            <p class="mb-2">
                                This leave type is currently being used and cannot be deleted.
                            </p>
                            <p class="mb-0">
                                <strong>Usage Details:</strong>
                            </p>
                            <ul class="mb-0">
                                {% if leave_requests_count > 0 %}
                                <li>{{ leave_requests_count }} leave request{{ leave_requests_count|pluralize }} exist for this leave type</li>
                                {% endif %}
                                {% if leave_balances_count > 0 %}
                                <li>{{ leave_balances_count }} leave balance record{{ leave_balances_count|pluralize }} exist for this leave type</li>
                                {% endif %}
                            </ul>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Required Actions Before Deletion</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">To delete this leave type, you must first:</p>
                                <ul class="mb-0">
                                    {% if leave_requests_count > 0 %}
                                    <li>Process or cancel all pending leave requests of this type</li>
                                    <li>Archive or transfer historical leave requests to another leave type</li>
                                    {% endif %}
                                    {% if leave_balances_count > 0 %}
                                    <li>Transfer or clear all leave balance records for this leave type</li>
                                    <li>Update employee leave allocations</li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'core:leave_type_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Leave Types
                            </a>
                            <a href="{% url 'core:leave_type_detail' leave_type.pk %}" class="btn btn-primary">
                                <i class="fas fa-eye me-2"></i>View Usage Details
                            </a>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Safe to Delete</h6>
                            <p class="mb-0">
                                This leave type has no associated leave requests or balance records and can be safely deleted.
                            </p>
                        </div>

                        <!-- Deletion Impact -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">What will happen when you delete this leave type?</h6>
                            </div>
                            <div class="card-body">
                                <ul class="mb-0">
                                    <li>The leave type record will be permanently removed</li>
                                    <li>Employees will no longer be able to apply for this type of leave</li>
                                    <li>This leave type will not appear in any dropdown menus</li>
                                    <li>This action cannot be undone</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Confirmation Form -->
                        <form method="POST" id="deleteForm">
                            {% csrf_token %}
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    I understand that this action cannot be undone and want to proceed with deleting 
                                    the leave type "{{ leave_type.name }}".
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{% url 'core:leave_type_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="fas fa-trash me-2"></i>Delete Leave Type
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>

            <!-- Alternative Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Alternative Actions
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Instead of deleting, you might want to:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{% url 'core:leave_type_edit' leave_type.pk %}" class="btn btn-outline-primary btn-sm mb-2">
                                <i class="fas fa-edit me-2"></i>Edit Leave Type Settings
                            </a>
                        </div>
                        <div class="col-md-6">
                            {% if leave_type.is_active %}
                            <button class="btn btn-outline-warning btn-sm mb-2" onclick="deactivateLeaveType()">
                                <i class="fas fa-pause me-2"></i>Deactivate Leave Type
                            </button>
                            {% else %}
                            <button class="btn btn-outline-success btn-sm mb-2" onclick="activateLeaveType()">
                                <i class="fas fa-play me-2"></i>Activate Leave Type
                            </button>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="{% url 'core:leave_type_detail' leave_type.pk %}" class="btn btn-outline-info btn-sm mb-2">
                                <i class="fas fa-eye me-2"></i>View Leave Type Details
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'core:leave_type_create' %}" class="btn btn-outline-success btn-sm mb-2">
                                <i class="fas fa-plus me-2"></i>Create New Leave Type
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.border-danger {
    border-color: #dc3545 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    if (confirmCheckbox && deleteButton) {
        confirmCheckbox.addEventListener('change', function() {
            deleteButton.disabled = !this.checked;
        });
        
        deleteForm.addEventListener('submit', function(e) {
            if (!confirmCheckbox.checked) {
                e.preventDefault();
                alert('Please confirm that you want to delete this leave type.');
                return false;
            }
            
            // Double confirmation
            const confirmed = confirm(
                'Are you absolutely sure you want to delete the leave type "{{ leave_type.name }}"? ' +
                'This action cannot be undone.'
            );
            
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        });
    }
});

function deactivateLeaveType() {
    if (confirm('Are you sure you want to deactivate this leave type? Employees will not be able to apply for this type of leave.')) {
        window.location.href = "{% url 'core:leave_type_edit' leave_type.pk %}";
    }
}

function activateLeaveType() {
    if (confirm('Are you sure you want to activate this leave type? Employees will be able to apply for this type of leave.')) {
        window.location.href = "{% url 'core:leave_type_edit' leave_type.pk %}";
    }
}
</script>
{% endblock %}
