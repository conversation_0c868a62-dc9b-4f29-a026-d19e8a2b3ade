{% extends 'base.html' %}

{% block title %}{{ department.name }} - Department Details - Smart Management System{% endblock %}

{% block content %}
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-building me-3"></i>{{ department.name }}
                        </h1>
                        <p class="lead mb-0">Department Details & Employee Management</p>
                    </div>
                    <div>
                        <a href="{% url 'core:department_list' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Departments
                        </a>
                        <a href="{% url 'core:department_edit' department.pk %}" class="btn btn-light">
                            <i class="fas fa-edit me-2"></i>Edit Department
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <!-- Department Information -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Department Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Department Name</h6>
                            <p class="text-muted">{{ department.name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Created Date</h6>
                            <p class="text-muted">{{ department.created_at|date:"M d, Y g:i A" }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6>Description</h6>
                            {% if department.description %}
                                <p class="text-muted">{{ department.description }}</p>
                            {% else %}
                                <p class="text-muted fst-italic">No description provided</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Last Updated</h6>
                            <p class="text-muted">{{ department.updated_at|date:"M d, Y g:i A" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Employees</span>
                            <span class="badge bg-primary">{{ total_employees }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Active Employees</span>
                            <span class="badge bg-success">{{ active_employees }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Inactive Employees</span>
                            <span class="badge bg-warning">{{ inactive_employees }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Terminated Employees</span>
                            <span class="badge bg-danger">{{ terminated_employees }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="mb-0">
                        <div class="d-flex justify-content-between">
                            <span>Recent Joiners (30 days)</span>
                            <span class="badge bg-info">{{ recent_joiners }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Management -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>Department Employees
            </h5>
            <span class="badge bg-primary">{{ employees.paginator.count }} employee{{ employees.paginator.count|pluralize }}</span>
        </div>
        
        <!-- Search and Filter -->
        <div class="card-body border-bottom">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="Search employees...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="All" {% if status_filter == 'All' %}selected{% endif %}>All Status</option>
                        <option value="Active" {% if status_filter == 'Active' %}selected{% endif %}>Active</option>
                        <option value="Inactive" {% if status_filter == 'Inactive' %}selected{% endif %}>Inactive</option>
                        <option value="Terminated" {% if status_filter == 'Terminated' %}selected{% endif %}>Terminated</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{% url 'core:department_detail' department.pk %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="card-body p-0">
            {% if employees %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Employee</th>
                                <th>Employee Code</th>
                                <th>Designation</th>
                                <th>Status</th>
                                <th>Joining Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ employee.user.get_full_name|default:employee.user.username }}</h6>
                                            <small class="text-muted">{{ employee.user.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ employee.employee_code }}</span>
                                </td>
                                <td>
                                    {% if employee.designation %}
                                        {{ employee.designation.title }}
                                    {% else %}
                                        <span class="text-muted fst-italic">Not assigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.status == 'Active' %}
                                        <span class="badge bg-success">{{ employee.status }}</span>
                                    {% elif employee.status == 'Inactive' %}
                                        <span class="badge bg-warning">{{ employee.status }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ employee.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ employee.date_of_joining|date:"M d, Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'core:employee_detail' employee.pk %}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'core:employee_edit' employee.pk %}" 
                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No employees found</h5>
                    {% if search_query or status_filter != 'Active' %}
                        <p class="text-muted">No employees match your search criteria.</p>
                        <a href="{% url 'core:department_detail' department.pk %}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    {% else %}
                        <p class="text-muted">This department doesn't have any employees yet.</p>
                        <a href="{% url 'core:employee_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Employee
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
        
        <!-- Pagination -->
        {% if employees.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="Employee pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if employees.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ employees.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ employees.number }} of {{ employees.paginator.num_pages }}
                        </span>
                    </li>
                    
                    {% if employees.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ employees.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ employees.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Last</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
</style>
{% endblock %}
