{% extends 'base.html' %}

{% block title %}Edit My Profile - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-user-edit me-3"></i>
                            Edit My Profile
                        </h1>
                        <p class="lead mb-0">Update your personal information</p>
                    </div>
                    <div>
                        <a href="{% url 'employee_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Edit Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>Personal Information
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> You can update your personal information below. Your username and role cannot be changed.
                        </div>

                        <form method="post" id="employeeEditForm">
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                            {{ form.first_name.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.first_name }}
                                        {% if form.first_name.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.first_name.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                            {{ form.last_name.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.last_name }}
                                        {% if form.last_name.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.last_name.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.phone.id_for_label }}" class="form-label">
                                            {{ form.phone.label }}
                                        </label>
                                        {{ form.phone }}
                                        {% if form.phone.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.phone.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                                            {{ form.date_of_birth.label }}
                                        </label>
                                        {{ form.date_of_birth }}
                                        {% if form.date_of_birth.errors %}
                                            <div class="text-danger mt-1">
                                                {% for error in form.date_of_birth.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    {{ form.address.label }}
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.address.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Read-only fields -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                        <small class="text-muted">Username cannot be changed</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Role</label>
                                        <input type="text" class="form-control" value="{{ user.get_role_display }}" readonly>
                                        <small class="text-muted">Role is assigned by admin</small>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-save me-2"></i>
                                    Update Profile
                                </button>
                                <a href="{% url 'employee_dashboard' %}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Profile Completion Card -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-pie me-2"></i>Profile Completion Tips
                        </h6>
                        <div class="row text-center">
                            <div class="col-md-4">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h6>Complete Profile</h6>
                                <small class="text-muted">Fill all fields for better experience</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-shield-alt fa-2x text-primary mb-2"></i>
                                <h6>Secure Information</h6>
                                <small class="text-muted">Your data is protected and secure</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-sync-alt fa-2x text-info mb-2"></i>
                                <h6>Keep Updated</h6>
                                <small class="text-muted">Update your info when needed</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('employeeEditForm').addEventListener('submit', function(e) {
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        submitBtn.disabled = true;
        
        // Re-enable button after a delay (in case of validation errors)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });

    // Form validation
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('employeeEditForm');
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
        
        function validateField(field) {
            const value = field.value.trim();
            const fieldName = field.name;
            
            // Remove existing validation feedback
            const existingFeedback = field.parentNode.querySelector('.validation-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            
            field.classList.remove('is-valid', 'is-invalid');
            
            // Basic validation
            if (field.hasAttribute('required') && !value) {
                showFieldError(field, 'This field is required.');
                return false;
            }
            
            if (fieldName === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    showFieldError(field, 'Please enter a valid email address.');
                    return false;
                }
            }
            
            if (fieldName === 'phone' && value) {
                const phoneRegex = /^[\d\s\-\+\(\)]+$/;
                if (!phoneRegex.test(value)) {
                    showFieldError(field, 'Please enter a valid phone number.');
                    return false;
                }
            }
            
            // If validation passes
            field.classList.add('is-valid');
            return true;
        }
        
        function showFieldError(field, message) {
            field.classList.add('is-invalid');
            const feedback = document.createElement('div');
            feedback.className = 'validation-feedback text-danger';
            feedback.innerHTML = '<small>' + message + '</small>';
            field.parentNode.appendChild(feedback);
        }
    });
</script>
{% endblock %}
